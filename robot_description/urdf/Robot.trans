<?xml version="1.0" ?>
<robot name="Robot" xmlns:xacro="http://www.ros.org/wiki/xacro" >

<transmission name="Revolute_5_tran">
  <type>transmission_interface/SimpleTransmission</type>
  <joint name="Revolute_5">
    <hardwareInterface>hardware_interface/VelocityJointInterface</hardwareInterface>
  </joint>
  <actuator name="Revolute_5_actr">
    <hardwareInterface>hardware_interface/VelocityJointInterface</hardwareInterface>
    <mechanicalReduction>1</mechanicalReduction>
  </actuator>
</transmission>

<transmission name="Revolute_6_tran">
  <type>transmission_interface/SimpleTransmission</type>
  <joint name="Revolute_6">
    <hardwareInterface>hardware_interface/VelocityJointInterface</hardwareInterface>
  </joint>
  <actuator name="Revolute_6_actr">
    <hardwareInterface>hardware_interface/VelocityJointInterface</hardwareInterface>
    <mechanicalReduction>1</mechanicalReduction>
  </actuator>
</transmission>

<transmission name="Revolute_7_tran">
  <type>transmission_interface/SimpleTransmission</type>
  <joint name="Revolute_7">
    <hardwareInterface>hardware_interface/VelocityJointInterface</hardwareInterface>
  </joint>
  <actuator name="Revolute_7_actr">
    <hardwareInterface>hardware_interface/VelocityJointInterface</hardwareInterface>
    <mechanicalReduction>1</mechanicalReduction>
  </actuator>
</transmission>

<transmission name="Revolute_8_tran">
  <type>transmission_interface/SimpleTransmission</type>
  <joint name="Revolute_8">
    <hardwareInterface>hardware_interface/VelocityJointInterface</hardwareInterface>
  </joint>
  <actuator name="Revolute_8_actr">
    <hardwareInterface>hardware_interface/VelocityJointInterface</hardwareInterface>
    <mechanicalReduction>1</mechanicalReduction>
  </actuator>
</transmission>

</robot>
