Panels:
  - Class: rviz/Displays
    Help Height: 78
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /Global Options1
        - /Status1
        - /RobotModel1
        - /TF1
      Splitter Ratio: 0.5
    Tree Height: 591
  - Class: rviz/Selection
    Name: Selection
  - Class: rviz/Tool Properties
    Expanded:
      - /2D Pose Estimate1
      - /2D Nav Goal1
      - /Publish Point1
    Name: Tool Properties
    Splitter Ratio: 0.5886790156364441
  - Class: rviz/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
  - Class: rviz/Time
    Experimental: false
    Name: Time
    SyncMode: 0
    SyncSource: ""
Preferences:
  PromptSaveOnExit: true
Toolbars:
  toolButtonStyle: 2
Visualization Manager:
  Class: ""
  Displays:
    - Alpha: 0.5
      Cell Size: 1
      Class: rviz/Grid
      Color: 160; 160; 164
      Enabled: true
      Line Style:
        Line Width: 0.029999999329447746
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 10
      Reference Frame: <Fixed Frame>
      Value: true
    - Alpha: 1
      Class: rviz/RobotModel
      Collision Enabled: false
      Enabled: true
      Links:
        2dofknuckle_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        2dofknuckle_link_10:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        2dofknuckle_link_2:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        2dofknuckle_link_3:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        2dofknuckle_link_4:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        2dofknuckle_link_5:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        2dofknuckle_link_6:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        2dofknuckle_link_7:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        2dofknuckle_link_8:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        2dofknuckle_link_9:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        All Links Enabled: true
        Expand Joint Details: false
        Expand Link Details: false
        Expand Tree: false
        Link Tree Style: Links in Alphabetic Order
        base_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        bucket_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        face_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        facebracket_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        hcsr04_cans_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        hcsr04_chips_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        hcsr04_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leg_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leg_link_2:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leg_link_3:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leg_link_4:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        lipo_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        mg90_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        mg90_link_10:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        mg90_link_2:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        mg90_link_3:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        mg90_link_4:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        mg90_link_5:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        mg90_link_6:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        mg90_link_7:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        mg90_link_8:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        mg90_link_9:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        mpu9250_cap_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        mpu9250_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        mpu9250_mpu_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        pizerow_cam_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        pizerow_con_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        pizerow_cpu_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        pizerow_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        raspicam_cam_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        raspicam_chips_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        raspicam_con_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        raspicam_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        servobottom_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        servobottom_link_10:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        servobottom_link_2:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        servobottom_link_3:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        servobottom_link_4:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        servobottom_link_5:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        servobottom_link_6:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        servobottom_link_7:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        servobottom_link_8:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        servobottom_link_9:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        shell_link_1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
      Name: RobotModel
      Robot Description: robot_description
      TF Prefix: ""
      Update Interval: 0
      Value: true
      Visual Enabled: true
    - Class: rviz/TF
      Enabled: false
      Frame Timeout: 15
      Frames:
        All Enabled: true
      Marker Scale: 0.5
      Name: TF
      Show Arrows: true
      Show Axes: true
      Show Names: true
      Tree:
        {}
      Update Interval: 0
      Value: false
  Enabled: true
  Global Options:
    Background Color: 48; 48; 48
    Default Light: true
    Fixed Frame: base_link
    Frame Rate: 30
  Name: root
  Tools:
    - Class: rviz/Interact
      Hide Inactive Objects: true
    - Class: rviz/MoveCamera
    - Class: rviz/Select
    - Class: rviz/FocusCamera
    - Class: rviz/Measure
    - Class: rviz/SetInitialPose
      Theta std deviation: 0.2617993950843811
      Topic: /initialpose
      X std deviation: 0.5
      Y std deviation: 0.5
    - Class: rviz/SetGoal
      Topic: /move_base_simple/goal
    - Class: rviz/PublishPoint
      Single click: true
      Topic: /clicked_point
  Value: true
  Views:
    Current:
      Class: rviz/Orbit
      Distance: 0.34426525235176086
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.05999999865889549
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Focal Point:
        X: -0.0016349668148905039
        Y: -0.014781012199819088
        Z: 0.017814036458730698
      Focal Shape Fixed Size: true
      Focal Shape Size: 0.05000000074505806
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.009999999776482582
      Pitch: 0.46039697527885437
      Target Frame: <Fixed Frame>
      Value: Orbit (rviz)
      Yaw: 0.593582272529602
    Saved: ~
Window Geometry:
  Displays:
    collapsed: false
  Height: 882
  Hide Left Dock: false
  Hide Right Dock: false
  QMainWindow State: 000000ff00000000fd000000040000000000000142000002d9fc0200000008fb0000001200530065006c0065006300740069006f006e00000001e10000009b0000005d00fffffffb0000001e0054006f006f006c002000500072006f007000650072007400690065007302000001ed000001df00000185000000a3fb000000120056006900650077007300200054006f006f02000001df000002110000018500000122fb000000200054006f006f006c002000500072006f0070006500720074006900650073003203000002880000011d000002210000017afb000000100044006900730070006c0061007900730100000037000002d9000000ca00fffffffb0000002000730065006c0065006300740069006f006e00200062007500660066006500720200000138000000aa0000023a00000294fb00000014005700690064006500530074006500720065006f02000000e6000000d2000003ee0000030bfb0000000c004b0069006e0065006300740200000186000001060000030c00000261000000010000010f000002d9fc0200000003fb0000001e0054006f006f006c002000500072006f00700065007200740069006500730100000041000000780000000000000000fb0000000a005600690065007700730100000037000002d9000000a100fffffffb0000001200530065006c0065006300740069006f006e010000025a000000b200000000000000000000000200000490000000a9fc0100000001fb0000000a00560069006500770073030000004e00000080000002e10000019700000003000004c000000044fc0100000002fb0000000800540069006d00650100000000000004c00000026b00fffffffb0000000800540069006d0065010000000000000450000000000000000000000267000002d900000004000000040000000800000008fc0000000100000002000000010000000a0054006f006f006c00730100000000ffffffff0000000000000000
  Selection:
    collapsed: false
  Time:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: false
  Width: 1216
  X: 700
  Y: 385
