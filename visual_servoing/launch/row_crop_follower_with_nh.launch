<launch>
  <!-- Load the parameters from the YAML file -->
  <rosparam file="$(find visual_servoing)/config/row_crop_config.yaml" command="load" />

  <!-- Original node configuration -->
  <node pkg="visual_servoing" type="row_crop_follower.py" name="row_crop_follower" output="screen">
    <param name="width" value="640" />
    <param name="height" value="480" />
    <param name="ex_Xc" value="320" />
    <param name="ex_Yc" value="240" />
    <param name="nh_L" value="150" />
    <param name="nh_H" value="250" />
    <param name="nh_offset" value="200" />
    <param name="steering_dir" value="1" />
  </node>
</launch>
