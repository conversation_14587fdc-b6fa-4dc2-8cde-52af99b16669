# 🤖 ROS Robot Control Web Interface

A React-based web interface for controlling ROS robots with **mobile device support**. Control your robot remotely through any web browser on your local network.

## 📋 Table of Contents

- [Prerequisites](#prerequisites)
- [Quick Start](#quick-start)
- [Detailed Setup](#detailed-setup)
- [Mobile Access](#mobile-access)
- [Troubleshooting](#troubleshooting)
- [Features](#features)
- [Configuration](#configuration)

## 🔧 Prerequisites

### System Requirements
- **ROS Noetic** installed and configured
- **Node.js 18+** and **npm**
- **rosbridge_server** package
- Robot and control device on same WiFi network

### Install Dependencies

```bash
# Install rosbridge_server (if not already installed)
sudo apt-get install ros-noetic-rosbridge-suite

# Verify Node.js version (should be 18+)
node --version

# If Node.js is outdated, update it:
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install nodejs -y
```

## 🚀 Quick Start

### 1. Navigate to Project Directory
```bash
cd ~/weednix_ws/src/react-ros-app
```

### 2. Install Node.js Dependencies (First Time Only)
```bash
npm install
```

### 3. Start ROS Bridge Server
```bash
# Open Terminal 1
roslaunch rosbridge_server rosbridge_websocket.launch
```

### 4. Start Web Interface
```bash
# Open Terminal 2
cd ~/weednix_ws/src/react-ros-app
npm start
```

### 5. Access the Interface
- **Local Access:** http://localhost:3000
- **Mobile Access:** http://***********:3000 (replace with your robot's IP)

## 📖 Detailed Setup

### Step 1: Prepare the Environment

```bash
# Navigate to the React app directory
cd ~/weednix_ws/src/react-ros-app

# Run the automatic setup script (recommended)
./setup-mobile-access.sh
```

**What this script does:**
- Detects your robot's IP address
- Configures environment variables
- Checks ROS bridge installation
- Sets up network access

### Step 2: Start ROS Bridge Server

**Terminal 1:**
```bash
# Source your ROS workspace
cd ~/weednix_ws
source devel/setup.bash

# Start the ROS bridge WebSocket server
roslaunch rosbridge_server rosbridge_websocket.launch
```

**Expected Output:**
```
[INFO] [timestamp]: Rosbridge WebSocket server started on port 9090
```

### Step 3: Start React Development Server

**Terminal 2:**
```bash
# Navigate to React app directory
cd ~/weednix_ws/src/react-ros-app

# Start the web server with network access
npm start
```

**Expected Output:**
```
Compiled successfully!

You can now view react-ros-app in the browser.

  Local:            http://localhost:3000
  On Your Network:  http://***********:3000
```

### Step 4: Connect and Control

1. **Open browser** and navigate to the network URL
2. **Configure ROS connection** using: `ws://***********:9090`
3. **Start controlling** your robot!

## 📱 Mobile Access

### Setup for Phone/Tablet Access

1. **Ensure both devices are on the same WiFi network**
2. **Find your robot's IP address:**
   ```bash
   ./get-ip.sh
   ```

3. **On your mobile device:**
   - Open any web browser
   - Navigate to: `http://YOUR_ROBOT_IP:3000`
   - Example: `http://***********:3000`

### Mobile-Optimized Features
- ✅ Touch-friendly joystick controls
- ✅ Responsive design for phone screens
- ✅ Quick connection buttons
- ✅ Real-time robot visualization
- ✅ Network status indicators

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### 1. "Cannot access from phone"
```bash
# Check if server is running and accessible
./fix-mobile-access.sh

# Verify firewall settings
sudo ufw status
sudo ufw allow 3000
sudo ufw allow 9090
```

#### 2. "ROS connection failed"
```bash
# Verify ROS bridge is running
rostopic list
rosnode list | grep rosbridge

# Check if WebSocket is accessible
curl -I http://localhost:9090
```

#### 3. "Server won't start"
```bash
# Check Node.js version
node --version  # Should be 18+

# Clear npm cache and reinstall
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

#### 4. "Network access not working"
```bash
# Test network connectivity
ping ***********  # From phone/other device

# Check if port is open
ss -tlnp | grep :3000

# Try different port
HOST=0.0.0.0 PORT=8080 npm start
```

### Diagnostic Tools

```bash
# Get robot IP address
./get-ip.sh

# Run comprehensive diagnostics
./fix-mobile-access.sh

# Check server status
ss -tlnp | grep :3000
ps aux | grep node
```

## ✨ Features

### Robot Control
- **Joystick Control:** Touch-friendly virtual joystick
- **Speed Control:** Adjustable linear and angular speeds
- **Emergency Stop:** Quick stop functionality

### Visualization
- **Real-time Position:** Live robot position tracking
- **Path Visualization:** Shows robot's movement history
- **Grid and Axes:** Coordinate reference system
- **Zoom Controls:** Adjustable visualization scale

### Network Features
- **Auto IP Detection:** Automatically finds robot's network address
- **Connection Status:** Real-time ROS connection monitoring
- **Quick Connect:** One-click connection buttons
- **Mobile Optimized:** Responsive design for all devices

## ⚙️ Configuration

### Environment Variables (.env)
```bash
# Network Configuration
HOST=0.0.0.0                                    # Allow network access
PORT=3000                                       # Web server port

# ROS Configuration
REACT_APP_DEFAULT_ROS_URL=ws://***********:9090 # Default ROS bridge URL
REACT_APP_ROBOT_IP=***********                 # Robot's IP address

# UI Configuration
REACT_APP_SHOW_NETWORK_INFO=true               # Show network info in UI
```

### Package.json Scripts
```bash
npm start        # Start with network access (0.0.0.0)
npm run start-local  # Start localhost only
npm run build    # Create production build
npm test         # Run tests
```

### ROS Topics Used
- `/cmd_vel` (geometry_msgs/Twist): For sending velocity commands to the robot
- `/odometry/filtered` (nav_msgs/Odometry): For receiving EKF-based localization data

## 🔄 Starting and Stopping

### To Start Everything:
```bash
# Terminal 1: ROS Bridge
roslaunch rosbridge_server rosbridge_websocket.launch

# Terminal 2: Web Server
cd ~/weednix_ws/src/react-ros-app && npm start
```

### To Stop Everything:
```bash
# In each terminal, press:
Ctrl + C
```

### To Restart:
```bash
# Stop both servers first (Ctrl+C)
# Then restart ROS bridge first, web server second
```

## 📞 Support

### If you encounter issues:

1. **Check the troubleshooting section above**
2. **Run diagnostic tools:**
   ```bash
   ./fix-mobile-access.sh
   ./get-ip.sh
   ```
3. **Verify network connectivity between devices**
4. **Check browser console for JavaScript errors**
5. **Ensure ROS topics are publishing data**

### Useful Commands:
```bash
# Check ROS topics
rostopic list
rostopic echo /cmd_vel
rostopic echo /odometry/filtered

# Check network
ip addr show
ss -tlnp | grep :3000
ss -tlnp | grep :9090

# Check processes
ps aux | grep rosbridge
ps aux | grep node
```

---

**Happy Robot Controlling! 🤖📱**

For more detailed troubleshooting, see `TROUBLESHOOTING.md` and `MOBILE_ACCESS.md`.
