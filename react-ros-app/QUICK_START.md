# 🚀 Quick Start Guide - ROS Robot Control Web Interface

## ⚡ Essential Commands

### 1. Setup (First Time Only)
```bash
cd ~/weednix_ws/src/react-ros-app
./setup-mobile-access.sh
npm install
```

### 2. Start Robot Control Interface

**Terminal 1 - ROS Bridge:**
```bash
roslaunch rosbridge_server rosbridge_websocket.launch
```

**Terminal 2 - Web Server:**
```bash
cd ~/weednix_ws/src/react-ros-app
npm start
```

### 3. Access the Interface

**From Computer:**
- http://localhost:3000

**From Phone/Tablet:**
- http://***********:3000 (replace with your robot's IP)

**ROS Connection URL:**
- ws://***********:9090

## 🔧 Troubleshooting Commands

```bash
# Get robot IP
./get-ip.sh

# Fix mobile access issues
./fix-mobile-access.sh

# Check if servers are running
ss -tlnp | grep :3000  # Web server
ss -tlnp | grep :9090  # ROS bridge

# Check ROS topics
rostopic list
rostopic echo /cmd_vel
```

## 🛑 Stop Everything

```bash
# In each terminal, press:
Ctrl + C
```

## 📱 Mobile Access Checklist

- ✅ Both devices on same WiFi network
- ✅ ROS bridge running (port 9090)
- ✅ Web server running (port 3000)
- ✅ Use robot's IP address, not localhost
- ✅ Try different browser if issues

## 🆘 Common Issues

**Can't access from phone?**
```bash
sudo ufw allow 3000
sudo ufw allow 9090
```

**Server won't start?**
```bash
node --version  # Should be 18+
npm cache clean --force
npm install
```

**ROS connection failed?**
```bash
rostopic list
rosnode list | grep rosbridge
```

---

**Need more help?** See the full `README.md` for detailed documentation.
