.mode-control-container {
  background: white;
  border-radius: 10px;
  padding: 20px;
  margin: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 2px solid #e0e0e0;
}

.mode-control-container h2 {
  color: #333;
  margin-bottom: 15px;
  text-align: center;
  font-size: 1.4em;
}

.mode-status {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  border-left: 4px solid #007bff;
}

.mode-status p {
  margin: 5px 0;
  font-size: 0.9em;
}

.mode-indicator {
  padding: 4px 12px;
  border-radius: 20px;
  font-weight: bold;
  margin-left: 8px;
  font-size: 0.85em;
}

.mode-indicator.manual {
  background: #28a745;
  color: white;
}

.mode-indicator.autonomous {
  background: #007bff;
  color: white;
}

.status-active {
  color: #28a745;
  font-weight: bold;
}

.mode-buttons {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  justify-content: center;
}

.mode-btn {
  flex: 1;
  max-width: 200px;
  padding: 20px 15px;
  border: 2px solid #ddd;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  font-family: inherit;
}

.mode-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.mode-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.mode-btn.manual {
  border-color: #28a745;
}

.mode-btn.manual:hover:not(:disabled) {
  background: #f8fff9;
  border-color: #1e7e34;
}

.mode-btn.manual.active {
  background: #28a745;
  color: white;
  border-color: #1e7e34;
}

.mode-btn.autonomous {
  border-color: #007bff;
}

.mode-btn.autonomous:hover:not(:disabled) {
  background: #f8f9ff;
  border-color: #0056b3;
}

.mode-btn.autonomous.active {
  background: #007bff;
  color: white;
  border-color: #0056b3;
}

.mode-icon {
  font-size: 2em;
  margin-bottom: 8px;
}

.mode-label {
  font-weight: bold;
  font-size: 1.1em;
  margin-bottom: 5px;
}

.mode-description {
  font-size: 0.85em;
  opacity: 0.8;
}

.mode-btn.active .mode-description {
  opacity: 1;
}

.mode-info {
  margin-top: 15px;
}

.info-panel {
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid;
}

.info-panel.manual {
  background: #f8fff9;
  border-left-color: #28a745;
}

.info-panel.autonomous {
  background: #f8f9ff;
  border-left-color: #007bff;
}

.info-panel h3 {
  margin: 0 0 10px 0;
  font-size: 1.1em;
}

.info-panel p {
  margin: 5px 0;
  font-size: 0.9em;
}

/* Responsive design */
@media (max-width: 768px) {
  .mode-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .mode-btn {
    max-width: 100%;
    width: 100%;
  }
  
  .mode-control-container {
    margin: 5px;
    padding: 15px;
  }
}
