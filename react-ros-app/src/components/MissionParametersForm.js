import React, { useState } from 'react';
import './MissionParametersForm.css';

const MissionParametersForm = ({ mapData, onLaunchMission, onBack }) => {
  const [parameters, setParameters] = useState({
    row_length: 50,
    row_spacing: 0.75,
    field_direction: 'right',
    speed: 1.0,
    laser_power: 80,
    detection_sensitivity: 0.7
  });

  const [errors, setErrors] = useState({});

  const handleParameterChange = (key, value) => {
    setParameters(prev => ({
      ...prev,
      [key]: value
    }));
    
    // Clear error when user starts typing
    if (errors[key]) {
      setErrors(prev => ({
        ...prev,
        [key]: null
      }));
    }
  };

  const validateParameters = () => {
    const newErrors = {};

    if (parameters.row_length <= 0) {
      newErrors.row_length = 'Row length must be greater than 0';
    }

    if (parameters.row_spacing <= 0) {
      newErrors.row_spacing = 'Row spacing must be greater than 0';
    }

    if (parameters.speed <= 0 || parameters.speed > 5) {
      newErrors.speed = 'Speed must be between 0.1 and 5.0 m/s';
    }

    if (parameters.laser_power < 10 || parameters.laser_power > 100) {
      newErrors.laser_power = 'Laser power must be between 10 and 100%';
    }

    if (parameters.detection_sensitivity < 0.1 || parameters.detection_sensitivity > 1.0) {
      newErrors.detection_sensitivity = 'Detection sensitivity must be between 0.1 and 1.0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLaunchMission = async () => {
    if (validateParameters()) {
      // Save parameters to server before launching mission
      await saveParameters();

      onLaunchMission({
        mapData,
        parameters
      });
    }
  };

  const saveParameters = async () => {
    try {
      // Generate filename based on map name
      const mapName = mapData?.name || 'unknown_map';
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
      const filename = `${mapName.toLowerCase().replace(/\s+/g, '_')}_${timestamp}.yaml`;

      const response = await fetch('/api/save-parameters', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filename: filename,
          parameters: parameters,
          mapName: mapName
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save parameters to server');
      }

      const result = await response.json();
      console.log('Parameters saved to server:', result);

      // Show success message briefly
      alert(`Mission parameters saved successfully!\n\nFile: ${result.filename}\nLocation: visual_servoing/config/`);

    } catch (error) {
      console.error('Error saving parameters:', error);
      alert(`Warning: Failed to save parameters: ${error.message}\n\nThe mission will continue, but parameters were not saved.`);
    }
  };

  const calculateEstimatedTime = () => {
    // Simple estimation based on field area and speed
    if (mapData?.boundary?.length >= 4) {
      // Rough area calculation (simplified)
      const area = 1000; // placeholder - would calculate actual area from boundary
      const estimatedTime = (area / parameters.speed) / 60; // minutes
      return Math.round(estimatedTime);
    }
    return 'N/A';
  };

  return (
    <div className="mission-parameters-page">
      <header className="parameters-header">
        <button className="back-button" onClick={onBack}>← Back</button>
        <h1>Mission Parameters</h1>
        <p>Configure your weeding mission settings</p>
      </header>

      <div className="parameters-container">
        <div className="parameters-form">
          <div className="form-section">
            <h3>🌾 Field Configuration</h3>
            
            <div className="form-group">
              <label htmlFor="row_length">Row Length (m)</label>
              <input
                type="number"
                id="row_length"
                value={parameters.row_length}
                onChange={(e) => handleParameterChange('row_length', parseFloat(e.target.value))}
                min="1"
                max="1000"
                step="0.1"
                className={errors.row_length ? 'error' : ''}
              />
              {errors.row_length && <span className="error-text">{errors.row_length}</span>}
              <small>Maximum length of each row to process</small>
            </div>

            <div className="form-group">
              <label htmlFor="row_spacing">Row Spacing (m)</label>
              <input
                type="number"
                id="row_spacing"
                value={parameters.row_spacing}
                onChange={(e) => handleParameterChange('row_spacing', parseFloat(e.target.value))}
                min="0.1"
                max="5"
                step="0.05"
                className={errors.row_spacing ? 'error' : ''}
              />
              {errors.row_spacing && <span className="error-text">{errors.row_spacing}</span>}
              <small>Distance between crop rows</small>
            </div>

            <div className="form-group">
              <label htmlFor="field_direction">Field Direction</label>
              <select
                id="field_direction"
                value={parameters.field_direction}
                onChange={(e) => handleParameterChange('field_direction', e.target.value)}
              >
                <option value="right">Right</option>
                <option value="left">Left</option>
              </select>
              <small>Direction to process the field</small>
            </div>
          </div>

          <div className="form-section">
            <h3>🤖 Robot Configuration</h3>
            
            <div className="form-group">
              <label htmlFor="speed">Operating Speed (m/s)</label>
              <input
                type="number"
                id="speed"
                value={parameters.speed}
                onChange={(e) => handleParameterChange('speed', parseFloat(e.target.value))}
                min="0.1"
                max="5"
                step="0.1"
                className={errors.speed ? 'error' : ''}
              />
              {errors.speed && <span className="error-text">{errors.speed}</span>}
              <small>Robot movement speed during operation</small>
            </div>

            <div className="form-group">
              <label htmlFor="laser_power">Laser Power (%)</label>
              <input
                type="number"
                id="laser_power"
                value={parameters.laser_power}
                onChange={(e) => handleParameterChange('laser_power', parseInt(e.target.value))}
                min="10"
                max="100"
                step="5"
                className={errors.laser_power ? 'error' : ''}
              />
              {errors.laser_power && <span className="error-text">{errors.laser_power}</span>}
              <small>Laser intensity for weed elimination</small>
            </div>

            <div className="form-group">
              <label htmlFor="detection_sensitivity">Detection Sensitivity</label>
              <input
                type="number"
                id="detection_sensitivity"
                value={parameters.detection_sensitivity}
                onChange={(e) => handleParameterChange('detection_sensitivity', parseFloat(e.target.value))}
                min="0.1"
                max="1.0"
                step="0.1"
                className={errors.detection_sensitivity ? 'error' : ''}
              />
              {errors.detection_sensitivity && <span className="error-text">{errors.detection_sensitivity}</span>}
              <small>Weed detection sensitivity (0.1 = low, 1.0 = high)</small>
            </div>
          </div>
        </div>

        <div className="mission-summary">
          <h3>📋 Mission Summary</h3>
          
          <div className="summary-item">
            <strong>Map:</strong> {mapData?.name || 'Selected Map'}
          </div>
          
          <div className="summary-item">
            <strong>Boundary Points:</strong> {mapData?.boundary?.length || 0}
          </div>
          
          <div className="summary-item">
            <strong>Row Configuration:</strong> {parameters.row_length}m × {parameters.row_spacing}m spacing
          </div>
          
          <div className="summary-item">
            <strong>Direction:</strong> {parameters.field_direction}
          </div>
          
          <div className="summary-item">
            <strong>Operating Speed:</strong> {parameters.speed} m/s
          </div>
          
          <div className="summary-item">
            <strong>Laser Power:</strong> {parameters.laser_power}%
          </div>
          
          <div className="summary-item">
            <strong>Estimated Time:</strong> {calculateEstimatedTime()} minutes
          </div>

          <div className="safety-notice">
            <h4>⚠️ Safety Notice</h4>
            <ul>
              <li>Ensure the field is clear of people and animals</li>
              <li>Verify laser safety protocols are in place</li>
              <li>Monitor the robot during operation</li>
              <li>Emergency stop is available at all times</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="parameters-footer">
        <button className="back-btn" onClick={onBack}>
          ← Back to Map Setup
        </button>
        <button 
          className="launch-btn" 
          onClick={handleLaunchMission}
        >
          🚀 Launch Mission
        </button>
      </div>
    </div>
  );
};

export default MissionParametersForm;
