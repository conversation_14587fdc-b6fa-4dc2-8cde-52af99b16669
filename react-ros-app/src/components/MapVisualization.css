.map-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 10px;
  margin: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.map-container h2 {
  margin-top: 0;
  color: #333;
}

.map-status {
  margin: 5px 0;
  padding: 5px 10px;
  background-color: #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  color: #555;
}

.map-controls {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin: 10px 0;
  padding: 10px;
  background-color: #e0e0e0;
  border-radius: 4px;
  width: 100%;
  max-width: 600px;
}

.control-group {
  margin: 0 15px;
}

.control-group label {
  display: flex;
  align-items: center;
  margin: 5px 0;
  font-size: 14px;
  color: #333;
}

.control-group input[type="checkbox"] {
  margin-right: 5px;
}

.control-group input[type="range"] {
  margin: 0 10px;
  width: 100px;
}

.clear-path-btn {
  margin-top: 10px;
  padding: 5px 10px;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-path-btn:hover {
  background-color: #c0392b;
}

.map-canvas {
  background-color: #f0f0f0;
  border: 1px solid #cccccc;
  margin: 10px 0;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.position-info {
  background-color: #e0e0e0;
  padding: 15px;
  border-radius: 4px;
  margin: 10px 0;
  text-align: center;
  font-size: 14px;
  width: 100%;
  max-width: 600px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.position-info h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #2980b9;
  font-size: 16px;
}

.position-info p {
  margin: 8px 0;
  font-family: monospace;
  font-size: 15px;
}

.map-legend {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 10px;
}

.legend-color {
  width: 20px;
  height: 20px;
  margin-right: 5px;
  border-radius: 3px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.map-instructions {
  margin-top: 15px;
  text-align: center;
  font-size: 14px;
  color: #666;
  max-width: 500px;
}

.map-instructions p {
  margin: 5px 0;
}
