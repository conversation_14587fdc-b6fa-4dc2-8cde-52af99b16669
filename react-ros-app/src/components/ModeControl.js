import React, { useContext, useEffect, useState } from 'react';
import ROS<PERSON><PERSON> from 'roslib';
import { RosContext } from './RosConnection';
import './ModeControl.css';

const ModeControl = ({ onModeChange }) => {
  const { ros, isConnected } = useContext(RosContext);
  const [currentMode, setCurrentMode] = useState('manual'); // 'manual' or 'autonomous'
  const [modeStatus, setModeStatus] = useState('Disconnected');
  const [visualServingActive, setVisualServingActive] = useState(false);

  // ROS publishers and services
  const modePublisherRef = React.useRef(null);
  const visualServingStatusSubscriberRef = React.useRef(null);

  // Initialize ROS connections
  useEffect(() => {
    if (!isConnected || !ros) {
      setModeStatus('Waiting for ROS connection...');
      return;
    }

    setModeStatus('Connected');

    // Create publisher for mode control
    modePublisherRef.current = new ROSLIB.Topic({
      ros: ros,
      name: '/robot_mode',
      messageType: 'std_msgs/String'
    });

    // Subscribe to visual servoing status (if available)
    visualServingStatusSubscriberRef.current = new ROSLIB.Topic({
      ros: ros,
      name: '/visual_servoing/status',
      messageType: 'std_msgs/Bool'
    });

    visualServingStatusSubscriberRef.current.subscribe((message) => {
      setVisualServingActive(message.data);
    });

    // Publish initial mode
    publishMode('manual');

    return () => {
      if (visualServingStatusSubscriberRef.current) {
        visualServingStatusSubscriberRef.current.unsubscribe();
      }
    };
  }, [ros, isConnected]);

  const publishMode = (mode) => {
    if (modePublisherRef.current) {
      const message = new ROSLIB.Message({
        data: mode
      });
      modePublisherRef.current.publish(message);
      console.log(`Published mode: ${mode}`);
    }
  };

  const handleModeChange = (newMode) => {
    setCurrentMode(newMode);
    publishMode(newMode);
    
    // Notify parent component about mode change
    if (onModeChange) {
      onModeChange(newMode);
    }

    setModeStatus(`Mode changed to: ${newMode}`);
  };

  const startVisualServoing = () => {
    // Call ROS service to start visual servoing
    if (ros) {
      const startService = new ROSLIB.Service({
        ros: ros,
        name: '/visual_servoing/start',
        serviceType: 'std_srvs/Empty'
      });

      const request = new ROSLIB.ServiceRequest({});

      startService.callService(request, (result) => {
        console.log('Visual servoing started successfully');
        setModeStatus('Visual servoing started');
      }, (error) => {
        console.warn('Visual servoing service not available, using topic method');
        // Fallback to topic-based control
        handleModeChange('autonomous');
      });
    }
  };

  const stopVisualServoing = () => {
    // Call ROS service to stop visual servoing
    if (ros) {
      const stopService = new ROSLIB.Service({
        ros: ros,
        name: '/visual_servoing/stop',
        serviceType: 'std_srvs/Empty'
      });

      const request = new ROSLIB.ServiceRequest({});

      stopService.callService(request, (result) => {
        console.log('Visual servoing stopped successfully');
        setModeStatus('Visual servoing stopped');
      }, (error) => {
        console.warn('Visual servoing service not available, using topic method');
        // Fallback to topic-based control
        handleModeChange('manual');
      });
    }
  };

  return (
    <div className="mode-control-container">
      <h2>Robot Operation Mode</h2>
      
      <div className="mode-status">
        <p><strong>Status:</strong> {modeStatus}</p>
        <p><strong>Current Mode:</strong> 
          <span className={`mode-indicator ${currentMode}`}>
            {currentMode.charAt(0).toUpperCase() + currentMode.slice(1)}
          </span>
        </p>
        {visualServingActive && (
          <p><strong>Visual Servoing:</strong> 
            <span className="status-active">Active</span>
          </p>
        )}
      </div>

      <div className="mode-buttons">
        <button
          className={`mode-btn manual ${currentMode === 'manual' ? 'active' : ''}`}
          onClick={() => {
            handleModeChange('manual');
            stopVisualServoing();
          }}
          disabled={!isConnected}
        >
          <div className="mode-icon">🎮</div>
          <div className="mode-label">Manual Mode</div>
          <div className="mode-description">Joystick control</div>
        </button>

        <button
          className={`mode-btn autonomous ${currentMode === 'autonomous' ? 'active' : ''}`}
          onClick={() => {
            handleModeChange('autonomous');
            startVisualServoing();
          }}
          disabled={!isConnected}
        >
          <div className="mode-icon">🤖</div>
          <div className="mode-label">Autonomous Mode</div>
          <div className="mode-description">Visual servoing</div>
        </button>
      </div>

      <div className="mode-info">
        {currentMode === 'manual' && (
          <div className="info-panel manual">
            <h3>Manual Mode Active</h3>
            <p>• Use the joystick to control the robot</p>
            <p>• Visual servoing is disabled</p>
            <p>• Full manual control of movement</p>
          </div>
        )}
        
        {currentMode === 'autonomous' && (
          <div className="info-panel autonomous">
            <h3>Autonomous Mode Active</h3>
            <p>• Robot follows crop rows automatically</p>
            <p>• Visual servoing is enabled</p>
            <p>• Joystick control is disabled</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ModeControl;
