import React, { useContext, useEffect, useRef, useState } from 'react';
import nipplejs from 'nipplejs';
import ROSLIB from 'roslib';
import { RosContext } from './RosConnection';
import './JoystickControl.css';

const JoystickControl = ({ robotMode = 'manual' }) => {
  const { ros, isConnected } = useContext(RosContext);
  const joystickContainerRef = useRef(null);
  const cmdVelPublisherRef = useRef(null);
  const joystickManagerRef = useRef(null);

  // State for adjustable speed limits
  const [maxLinearVelocity, setMaxLinearVelocity] = useState(1.5);
  const [maxAngularVelocity, setMaxAngularVelocity] = useState(3.0);

  // Speed adjustment functions
  const adjustLinearSpeed = (increment) => {
    setMaxLinearVelocity(prev => Math.max(0.1, Math.min(5.0, prev + increment)));
  };

  const adjustAngularSpeed = (increment) => {
    setMaxAngularVelocity(prev => Math.max(0.1, Math.min(10.0, prev + increment)));
  };

  // Initialize the joystick and ROS publisher
  useEffect(() => {
    if (!isConnected || !ros) return;

    // Create a publisher for the cmd_vel topic
    cmdVelPublisherRef.current = new ROSLIB.Topic({
      ros: ros,
      name: '/cmd_vel',
      messageType: 'geometry_msgs/Twist'
    });

    // Initialize the joystick if the container is available
    if (joystickContainerRef.current) {
      joystickManagerRef.current = nipplejs.create({
        zone: joystickContainerRef.current,
        mode: 'static',
        position: { left: '50%', top: '50%' },
        color: 'blue',
        size: 150
      });

      // Set up joystick event handlers
      joystickManagerRef.current.on('move', (event, data) => {
        // Only publish commands in manual mode
        if (robotMode !== 'manual') {
          return;
        }

        // Calculate linear and angular velocities based on joystick position
        // Using state values for max velocities

        // Calculate forward/backward movement (y-axis)
        const linearVelocity = Math.cos(data.angle.radian) * Math.min(1.0, data.distance / 50) * maxLinearVelocity;

        // Calculate rotation (x-axis)
        const angularVelocity = -Math.sin(data.angle.radian) * Math.min(1.0, data.distance / 50) * maxAngularVelocity;

        // Create and publish the Twist message
        const twist = new ROSLIB.Message({
          linear: {
            x: linearVelocity,
            y: 0,
            z: 0
          },
          angular: {
            x: 0,
            y: 0,
            z: angularVelocity
          }
        });

        cmdVelPublisherRef.current.publish(twist);
      });

      // Stop the robot when the joystick is released
      joystickManagerRef.current.on('end', () => {
        // Only publish stop command in manual mode
        if (robotMode !== 'manual') {
          return;
        }

        const twist = new ROSLIB.Message({
          linear: { x: 0, y: 0, z: 0 },
          angular: { x: 0, y: 0, z: 0 }
        });
        cmdVelPublisherRef.current.publish(twist);
      });
    }

    // Clean up on unmount
    return () => {
      if (joystickManagerRef.current) {
        joystickManagerRef.current.destroy();
      }
    };
  }, [ros, isConnected, maxLinearVelocity, maxAngularVelocity, robotMode]);

  return (
    <div className={`joystick-container ${robotMode === 'autonomous' ? 'disabled' : ''}`}>
      <h2>Robot Control</h2>
      {robotMode === 'autonomous' && (
        <div className="mode-warning">
          <p>⚠️ Manual control disabled - Robot is in autonomous mode</p>
        </div>
      )}

      {/* Speed Controls */}
      <div className="speed-controls">
        <div className="speed-control-group">
          <h3>Linear Speed</h3>
          <div className="speed-display">{maxLinearVelocity.toFixed(1)} m/s</div>
          <div className="speed-buttons">
            <button
              className="speed-btn decrease"
              onClick={() => adjustLinearSpeed(-0.1)}
              disabled={maxLinearVelocity <= 0.1}
            >
              -
            </button>
            <button
              className="speed-btn increase"
              onClick={() => adjustLinearSpeed(0.1)}
              disabled={maxLinearVelocity >= 5.0}
            >
              +
            </button>
          </div>
        </div>

        <div className="speed-control-group">
          <h3>Angular Speed</h3>
          <div className="speed-display">{maxAngularVelocity.toFixed(1)} rad/s</div>
          <div className="speed-buttons">
            <button
              className="speed-btn decrease"
              onClick={() => adjustAngularSpeed(-0.2)}
              disabled={maxAngularVelocity <= 0.1}
            >
              -
            </button>
            <button
              className="speed-btn increase"
              onClick={() => adjustAngularSpeed(0.2)}
              disabled={maxAngularVelocity >= 10.0}
            >
              +
            </button>
          </div>
        </div>
      </div>

      <div
        ref={joystickContainerRef}
        className="joystick"
        style={{ width: '200px', height: '200px' }}
      ></div>
      <div className="joystick-instructions">
        <p>Move the joystick to control the robot.</p>
        <p>Forward/Backward: Move up/down</p>
        <p>Rotate: Move left/right</p>
      </div>
    </div>
  );
};

export default JoystickControl;
