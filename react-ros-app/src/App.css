.App {
  text-align: center;
  font-family: Arial, sans-serif;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  font-size: calc(10px + 1vmin);
  color: white;
  flex-wrap: wrap;
}

.App-header h1 {
  margin: 0;
  flex-grow: 1;
  text-align: center;
}

.mission-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  font-size: 0.9rem;
}

.change-mode-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.3s ease;
}

.change-mode-btn:hover {
  background: #0056b3;
}

.connection-form {
  max-width: 600px;
  margin: 20px auto;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 15px;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

button {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 10px 2px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #45a049;
}

.connection-status {
  margin: 10px;
  padding: 10px;
}

.dashboard {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin: 0 auto;
  max-width: 1200px;
}

.dashboard-column {
  flex: 1;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}

@media (max-width: 768px) {
  .dashboard {
    flex-direction: column;
  }
}

/* Network Information Styles */
.network-info {
  background: #e8f4fd;
  border: 1px solid #bee5eb;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 2rem;
  text-align: left;
}

.network-info h3 {
  margin-top: 0;
  color: #0c5460;
  font-size: 1.1rem;
}

.info-section p {
  margin: 0.5rem 0;
}

.mobile-access {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 4px;
  padding: 1rem;
  margin: 1rem 0;
}

.mobile-access p:first-child {
  margin-top: 0;
  font-weight: bold;
  color: #155724;
}

.localhost-warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  padding: 1rem;
  margin: 1rem 0;
}

.localhost-warning p:first-child {
  margin-top: 0;
  font-weight: bold;
  color: #856404;
}

.localhost-warning ol {
  margin: 0.5rem 0 0 1rem;
  padding-left: 1rem;
}

.localhost-warning code {
  background: #f8f9fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  color: #e83e8c;
}

/* Form Enhancements */
.form-group small {
  display: block;
  margin-top: 0.5rem;
  color: #6c757d;
  font-size: 0.875rem;
}

/* Quick Connect Buttons */
.quick-connect {
  margin: 1rem 0;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 4px;
  text-align: left;
}

.quick-connect p {
  margin: 0 0 0.5rem 0;
  font-weight: bold;
}

.quick-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  margin: 0.25rem 0.25rem 0.25rem 0;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.quick-btn:hover {
  background: #0056b3;
}

.connect-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  width: 100%;
  margin-top: 1rem;
}

.connect-btn:hover {
  background: #218838;
}

/* Mobile Responsive Enhancements */
@media (max-width: 768px) {
  .connection-form {
    margin: 10px;
    padding: 15px;
    max-width: none;
  }

  .network-info {
    padding: 0.75rem;
  }

  .quick-btn {
    display: block;
    width: 100%;
    margin: 0.25rem 0;
  }

  .App-header {
    flex-direction: column;
    gap: 1rem;
  }

  .App-header h1 {
    font-size: 1.5rem;
    text-align: center;
  }

  .mission-info {
    order: -1;
    width: 100%;
    justify-content: center;
  }
}
