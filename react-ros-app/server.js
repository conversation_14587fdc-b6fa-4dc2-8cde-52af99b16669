const express = require('express');
const fs = require('fs');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Path to the config directories
const CONFIG_DIR = path.join(__dirname, '..', '..', 'weednix_sensors', 'config');
const VISUAL_SERVOING_CONFIG_DIR = path.join(__dirname, '..', '..', 'visual_servoing', 'config');

// Ensure config directories exist
if (!fs.existsSync(CONFIG_DIR)) {
  fs.mkdirSync(CONFIG_DIR, { recursive: true });
}

if (!fs.existsSync(VISUAL_SERVOING_CONFIG_DIR)) {
  fs.mkdirSync(VISUAL_SERVOING_CONFIG_DIR, { recursive: true });
}

// API endpoint to save GeoJSON files
app.post('/api/save-geojson', (req, res) => {
  try {
    const { filename, data } = req.body;
    
    if (!filename || !data) {
      return res.status(400).json({ error: 'Filename and data are required' });
    }

    // Validate filename
    if (!filename.endsWith('.geojson')) {
      return res.status(400).json({ error: 'Filename must end with .geojson' });
    }

    // Sanitize filename
    const sanitizedFilename = filename.replace(/[^a-zA-Z0-9._-]/g, '_');
    const filePath = path.join(CONFIG_DIR, sanitizedFilename);

    // Write file
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));

    console.log(`GeoJSON file saved: ${filePath}`);
    res.json({ 
      success: true, 
      message: 'File saved successfully',
      filename: sanitizedFilename,
      path: filePath
    });

  } catch (error) {
    console.error('Error saving GeoJSON file:', error);
    res.status(500).json({ 
      error: 'Failed to save file',
      details: error.message 
    });
  }
});

// API endpoint to list GeoJSON files
app.get('/api/list-geojson', (req, res) => {
  try {
    const files = fs.readdirSync(CONFIG_DIR)
      .filter(file => file.endsWith('.geojson'))
      .map(file => {
        const filePath = path.join(CONFIG_DIR, file);
        const stats = fs.statSync(filePath);
        return {
          filename: file,
          size: stats.size,
          modified: stats.mtime,
          created: stats.birthtime
        };
      });

    res.json({ files });
  } catch (error) {
    console.error('Error listing GeoJSON files:', error);
    res.status(500).json({ 
      error: 'Failed to list files',
      details: error.message 
    });
  }
});

// API endpoint to save parameter files (YAML)
app.post('/api/save-parameters', (req, res) => {
  try {
    const { filename, parameters, mapName } = req.body;

    if (!filename || !parameters) {
      return res.status(400).json({ error: 'Filename and parameters are required' });
    }

    // Validate filename
    if (!filename.endsWith('.yaml')) {
      return res.status(400).json({ error: 'Filename must end with .yaml' });
    }

    // Sanitize filename
    const sanitizedFilename = filename.replace(/[^a-zA-Z0-9._-]/g, '_');
    const filePath = path.join(VISUAL_SERVOING_CONFIG_DIR, sanitizedFilename);

    // Create YAML content
    const yamlContent = `# Mission parameters for ${mapName || 'Unknown Map'}
# Generated on ${new Date().toISOString()}

# --- FIELD PARAMETERS ---
row_length: ${parameters.row_length}          # (meters) The expected length of each crop row.
row_spacing: ${parameters.row_spacing}        # (meters) The distance between adjacent crop rows.
field_direction: "${parameters.field_direction}"   # ("right" or "left") The direction the robot should turn at the end of the row.

# --- ROW TRANSITION MANEUVER PARAMETERS ---
A: 1.0                   # (meters) Distance to move forward when exiting/entering a row.
theta_degrees: 45        # (degrees) Rotation angle (theta) for the turning maneuver.
linear_speed: ${parameters.speed}          # (m/s) Linear speed during the turning maneuver.
angular_speed: 2.0       # (rad/s) Angular speed during the turning maneuver.

# --- DETECTION PARAMETERS ---
detection_sensitivity: ${parameters.detection_sensitivity}  # Weed detection sensitivity (0.1 = low, 1.0 = high)
laser_power: ${parameters.laser_power}            # Laser power percentage (10-100%)

# --- DISTANCE TRACKING PARAMETERS ---
end_of_row_threshold: 0.5    # (meters) Distance threshold to detect end of row.
wrong_distance_threshold: 2.0 # (meters) Distance threshold to detect wrong direction.
use_odometry: true           # Use odometry for distance tracking.
use_feedback: true           # Use feedback control.

# --- NEIGHBOURHOOD TRACKER PARAMETERS ---
neighbourhood_width: 150     # (pixels) Width of the tracking window.
neighbourhood_height: 180    # (pixels) Height of the tracking window.
initial_x: 320               # (pixels) Initial X coordinate of the tracking window center.
initial_y: 240               # (pixels) Initial Y coordinate of the tracking window center.

# --- HSV COLOR FILTER PARAMETERS (for green crop rows) ---
h_min: 35                  # (degrees) Minimum Hue value.
h_max: 85                  # (degrees) Maximum Hue value.
s_min: 50                  # (0-255) Minimum Saturation value.
s_max: 255                 # (0-255) Maximum Saturation value.
v_min: 50                  # (0-255) Minimum Value (Brightness) value.
v_max: 255                 # (0-255) Maximum Value (Brightness) value.

# --- LINE FOLLOWING PARAMETERS ---
point_spacing: 70          # (pixels)  Spacing used when sampling points from the detected crop row.
kp: 0.005                # Proportional gain for steering control.
`;

    // Write file
    fs.writeFileSync(filePath, yamlContent);

    console.log(`Parameter file saved: ${filePath}`);
    res.json({
      success: true,
      message: 'Parameters saved successfully',
      filename: sanitizedFilename,
      path: filePath
    });

  } catch (error) {
    console.error('Error saving parameter file:', error);
    res.status(500).json({
      error: 'Failed to save parameters',
      details: error.message
    });
  }
});

// API endpoint to delete GeoJSON files
app.delete('/api/delete-geojson/:filename', (req, res) => {
  try {
    const { filename } = req.params;

    if (!filename.endsWith('.geojson')) {
      return res.status(400).json({ error: 'Invalid filename' });
    }

    const filePath = path.join(CONFIG_DIR, filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    fs.unlinkSync(filePath);

    console.log(`GeoJSON file deleted: ${filePath}`);
    res.json({
      success: true,
      message: 'File deleted successfully',
      filename
    });

  } catch (error) {
    console.error('Error deleting GeoJSON file:', error);
    res.status(500).json({
      error: 'Failed to delete file',
      details: error.message
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    configDir: CONFIG_DIR,
    visualServingConfigDir: VISUAL_SERVOING_CONFIG_DIR
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`GeoJSON API server running on port ${PORT}`);
  console.log(`Config directory: ${CONFIG_DIR}`);
});

module.exports = app;
