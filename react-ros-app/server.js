const express = require('express');
const fs = require('fs');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Path to the config directory
const CONFIG_DIR = path.join(__dirname, '..', '..', 'weednix_sensors', 'config');

// Ensure config directory exists
if (!fs.existsSync(CONFIG_DIR)) {
  fs.mkdirSync(CONFIG_DIR, { recursive: true });
}

// API endpoint to save GeoJSON files
app.post('/api/save-geojson', (req, res) => {
  try {
    const { filename, data } = req.body;
    
    if (!filename || !data) {
      return res.status(400).json({ error: 'Filename and data are required' });
    }

    // Validate filename
    if (!filename.endsWith('.geojson')) {
      return res.status(400).json({ error: 'Filename must end with .geojson' });
    }

    // Sanitize filename
    const sanitizedFilename = filename.replace(/[^a-zA-Z0-9._-]/g, '_');
    const filePath = path.join(CONFIG_DIR, sanitizedFilename);

    // Write file
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));

    console.log(`GeoJSON file saved: ${filePath}`);
    res.json({ 
      success: true, 
      message: 'File saved successfully',
      filename: sanitizedFilename,
      path: filePath
    });

  } catch (error) {
    console.error('Error saving GeoJSON file:', error);
    res.status(500).json({ 
      error: 'Failed to save file',
      details: error.message 
    });
  }
});

// API endpoint to list GeoJSON files
app.get('/api/list-geojson', (req, res) => {
  try {
    const files = fs.readdirSync(CONFIG_DIR)
      .filter(file => file.endsWith('.geojson'))
      .map(file => {
        const filePath = path.join(CONFIG_DIR, file);
        const stats = fs.statSync(filePath);
        return {
          filename: file,
          size: stats.size,
          modified: stats.mtime,
          created: stats.birthtime
        };
      });

    res.json({ files });
  } catch (error) {
    console.error('Error listing GeoJSON files:', error);
    res.status(500).json({ 
      error: 'Failed to list files',
      details: error.message 
    });
  }
});

// API endpoint to delete GeoJSON files
app.delete('/api/delete-geojson/:filename', (req, res) => {
  try {
    const { filename } = req.params;
    
    if (!filename.endsWith('.geojson')) {
      return res.status(400).json({ error: 'Invalid filename' });
    }

    const filePath = path.join(CONFIG_DIR, filename);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    fs.unlinkSync(filePath);
    
    console.log(`GeoJSON file deleted: ${filePath}`);
    res.json({ 
      success: true, 
      message: 'File deleted successfully',
      filename 
    });

  } catch (error) {
    console.error('Error deleting GeoJSON file:', error);
    res.status(500).json({ 
      error: 'Failed to delete file',
      details: error.message 
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    configDir: CONFIG_DIR
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`GeoJSON API server running on port ${PORT}`);
  console.log(`Config directory: ${CONFIG_DIR}`);
});

module.exports = app;
