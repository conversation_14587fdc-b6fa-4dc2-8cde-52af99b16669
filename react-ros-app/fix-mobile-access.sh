#!/bin/bash

# Script to diagnose and fix mobile access issues

echo "🔧 Mobile Access Diagnostic & Fix Tool"
echo "======================================"

# Get IP address
get_ip() {
    IP=$(hostname -I | awk '{print $1}' 2>/dev/null)
    if [ -z "$IP" ]; then
        IP=$(ip route get 1 2>/dev/null | awk '{print $7}' | head -1)
    fi
    echo "$IP"
}

ROBOT_IP=$(get_ip)
echo "📡 Robot IP: $ROBOT_IP"

# Check if server is running
echo ""
echo "🔍 Checking server status..."
if ss -tlnp | grep -q ":3000"; then
    echo "✅ Server is running on port 3000"
    
    # Check if it's bound to 0.0.0.0
    if ss -tlnp | grep ":3000" | grep -q "0.0.0.0"; then
        echo "✅ Server is accessible from network (0.0.0.0:3000)"
    else
        echo "❌ Server only listening on localhost"
        echo "💡 Fix: Stop server and run 'HOST=0.0.0.0 npm start'"
        exit 1
    fi
else
    echo "❌ Server is not running"
    echo "💡 Fix: Run 'npm start' to start the server"
    exit 1
fi

# Test local access
echo ""
echo "🧪 Testing local access..."
if curl -s -I http://localhost:3000 | grep -q "200 OK"; then
    echo "✅ Local access works"
else
    echo "❌ Local access failed"
    exit 1
fi

# Test network access
echo ""
echo "🧪 Testing network access..."
if curl -s -I http://$ROBOT_IP:3000 | grep -q "200 OK"; then
    echo "✅ Network access works from robot"
else
    echo "❌ Network access failed from robot"
    exit 1
fi

# Check firewall
echo ""
echo "🔥 Checking firewall..."
if command -v ufw >/dev/null 2>&1; then
    UFW_STATUS=$(sudo ufw status 2>/dev/null | head -1)
    if echo "$UFW_STATUS" | grep -q "Status: active"; then
        echo "⚠️  UFW firewall is active"
        
        # Check if port 3000 is allowed
        if sudo ufw status | grep -q "3000"; then
            echo "✅ Port 3000 is allowed in firewall"
        else
            echo "❌ Port 3000 is NOT allowed in firewall"
            echo ""
            echo "🔧 FIXING: Adding firewall rules..."
            sudo ufw allow 3000
            sudo ufw allow 9090
            echo "✅ Added firewall rules for ports 3000 and 9090"
        fi
    else
        echo "✅ UFW firewall is inactive"
    fi
else
    echo "ℹ️  UFW not installed, checking iptables..."
    if iptables -L | grep -q "REJECT\|DROP"; then
        echo "⚠️  iptables rules detected - may need manual configuration"
    else
        echo "✅ No obvious firewall blocking"
    fi
fi

# Network connectivity tips
echo ""
echo "📱 Mobile Access Instructions:"
echo "=============================="
echo "1. Connect your phone to the same WiFi network"
echo "2. Open browser on phone and go to:"
echo "   http://$ROBOT_IP:3000"
echo ""
echo "🔍 If still not working, try:"
echo "- Different browser on phone"
echo "- Clear browser cache"
echo "- Check if phone can ping $ROBOT_IP"
echo "- Restart WiFi on phone"
echo "- Try from another device first"
echo ""

# Test with curl if available
echo "🧪 Quick network test:"
echo "Run this from another device on the network:"
echo "curl -I http://$ROBOT_IP:3000"
echo ""

# Final status
echo "📊 Current Status:"
echo "=================="
echo "✅ Server running: YES"
echo "✅ Network binding: YES (0.0.0.0:3000)"
echo "✅ Local access: YES"
echo "✅ Network access: YES"
echo "✅ Firewall configured: YES"
echo ""
echo "📱 Mobile URL: http://$ROBOT_IP:3000"
echo "🤖 ROS Bridge: ws://$ROBOT_IP:9090"
echo ""
echo "🎉 Setup should be working! Try accessing from your phone now."
