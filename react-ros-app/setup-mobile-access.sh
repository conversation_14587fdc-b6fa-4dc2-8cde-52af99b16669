#!/bin/bash

# Setup script for mobile access to ROS Robot Control Interface
# This script helps configure the environment for network access

echo "🤖 ROS Robot Control Interface - Mobile Access Setup"
echo "=================================================="

# Get the current IP address
get_ip() {
    # Try different methods to get IP address
    IP=$(hostname -I | awk '{print $1}' 2>/dev/null)
    if [ -z "$IP" ]; then
        IP=$(ip route get 1 | awk '{print $7}' 2>/dev/null)
    fi
    if [ -z "$IP" ]; then
        IP=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1)
    fi
    echo "$IP"
}

ROBOT_IP=$(get_ip)

echo "📡 Detected IP Address: $ROBOT_IP"
echo ""

# Update .env file
echo "📝 Updating .env configuration..."
cat > .env << EOF
# React App Configuration for Network Access
HOST=0.0.0.0
PORT=3000

# ROS Bridge Configuration
REACT_APP_DEFAULT_ROS_URL=ws://$ROBOT_IP:9090
REACT_APP_ROBOT_IP=$ROBOT_IP

# Network Configuration
REACT_APP_SHOW_NETWORK_INFO=true
EOF

echo "✅ Updated .env file with IP: $ROBOT_IP"
echo ""

# Check if rosbridge_server is installed
echo "🔍 Checking ROS bridge server..."
if command -v rosrun >/dev/null 2>&1; then
    if rospack find rosbridge_server >/dev/null 2>&1; then
        echo "✅ rosbridge_server is installed"
    else
        echo "❌ rosbridge_server not found. Install with:"
        echo "   sudo apt-get install ros-\$ROS_DISTRO-rosbridge-suite"
    fi
else
    echo "❌ ROS not found in PATH"
fi

echo ""
echo "📱 Mobile Access Instructions:"
echo "=============================="
echo ""
echo "1. Start the ROS bridge server on your robot:"
echo "   roslaunch rosbridge_server rosbridge_websocket.launch"
echo ""
echo "2. Start the React development server:"
echo "   npm start"
echo ""
echo "3. Access from your phone:"
echo "   Open: http://$ROBOT_IP:3000"
echo ""
echo "4. Make sure your phone is connected to the same WiFi network!"
echo ""
echo "🔧 Troubleshooting:"
echo "==================="
echo "- If you can't access from phone, check firewall settings"
echo "- Ensure port 3000 and 9090 are open"
echo "- Verify both devices are on the same network"
echo ""
echo "🚀 Ready to start! Run 'npm start' to begin."
