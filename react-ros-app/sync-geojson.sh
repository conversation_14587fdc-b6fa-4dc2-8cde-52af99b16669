#!/bin/bash

# <PERSON><PERSON>t to sync GeoJSON files from config directory to React public directory
# This ensures the React app can access the latest map files

CONFIG_DIR="/home/<USER>/weednix_ws/src/weednix_sensors/config"
PUBLIC_DIR="/home/<USER>/weednix_ws/src/react-ros-app/public/weednix_sensors/config"

# Create public directory if it doesn't exist
mkdir -p "$PUBLIC_DIR"

# Copy all GeoJSON files
if [ -d "$CONFIG_DIR" ]; then
    echo "Syncing GeoJSON files from $CONFIG_DIR to $PUBLIC_DIR"
    cp "$CONFIG_DIR"/*.geojson "$PUBLIC_DIR/" 2>/dev/null || echo "No GeoJSON files found in config directory"
    echo "Sync complete. Available files:"
    ls -la "$PUBLIC_DIR"/*.geojson 2>/dev/null || echo "No GeoJSON files in public directory"
else
    echo "Config directory not found: $CONFIG_DIR"
fi
