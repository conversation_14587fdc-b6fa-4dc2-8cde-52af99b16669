# 🔧 Mobile Access Troubleshooting Guide

## ✅ Current Status
- ✅ React server is running on port 3000
- ✅ Server is listening on 0.0.0.0:3000 (network accessible)
- ✅ Local access works: http://localhost:3000
- ✅ Network access works from robot: http://***********:3000
- ❌ Phone cannot access: http://***********:3000

## 🔍 Most Common Issues & Solutions

### 1. **Firewall Blocking Access** (Most Likely)

**Check firewall status:**
```bash
sudo ufw status
```

**Solution A - Allow specific port:**
```bash
sudo ufw allow 3000
sudo ufw allow 9090  # For ROS bridge
```

**Solution B - Temporarily disable firewall (for testing):**
```bash
sudo ufw disable
# Test mobile access, then re-enable:
sudo ufw enable
```

### 2. **Network Connectivity Issues**

**Test from phone:**
- Connect phone to same WiFi network
- Try pinging the robot: `ping ***********`
- Use a network scanner app to verify robot IP

**Test from robot:**
```bash
# Check if port is accessible
curl -I http://***********:3000

# Check network interfaces
ip addr show

# Verify correct IP
hostname -I
```

### 3. **Router/Network Configuration**

**Common issues:**
- Guest network isolation
- AP isolation enabled
- Different subnets
- Corporate network restrictions

**Solutions:**
- Ensure both devices on same network
- Check router settings for device isolation
- Try mobile hotspot for testing

### 4. **Browser/Phone Issues**

**Try different approaches:**
- Different browser on phone
- Clear browser cache
- Try HTTP instead of HTTPS
- Disable VPN on phone
- Check phone's WiFi settings

### 5. **React Development Server Issues**

**Restart with explicit host binding:**
```bash
# Stop current server (Ctrl+C)
# Then start with explicit host:
HOST=0.0.0.0 PORT=3000 npm start
```

**Alternative start methods:**
```bash
# Method 1: Environment variable
export HOST=0.0.0.0 && npm start

# Method 2: Direct command
npx react-scripts start --host 0.0.0.0
```

## 🧪 Quick Tests

### Test 1: Verify Server Status
```bash
ss -tlnp | grep :3000
# Should show: 0.0.0.0:3000
```

### Test 2: Local Network Test
```bash
curl -I http://***********:3000
# Should return HTTP 200 OK
```

### Test 3: Phone Network Test
From phone browser, try:
- `http://***********:3000`
- `http://***********:3000/static/js/bundle.js`

### Test 4: Alternative IP
```bash
# Get all IPs
ip addr show | grep inet
# Try different IP if multiple available
```

## 🔧 Advanced Troubleshooting

### Check iptables (if ufw doesn't work)
```bash
sudo iptables -L -n
# Look for REJECT/DROP rules on port 3000
```

### Network debugging
```bash
# Check routing
ip route

# Check if service is bound correctly
netstat -tlnp | grep :3000  # (install: sudo apt install net-tools)
```

### React server logs
Check terminal output for any errors or warnings when starting the server.

## 📱 Phone-Specific Checks

1. **WiFi Connection:**
   - Same network as robot
   - Not on guest network
   - Strong signal strength

2. **Browser Settings:**
   - JavaScript enabled
   - No ad blockers interfering
   - Clear cache/cookies

3. **Network Apps:**
   - Use "Fing" or similar to scan network
   - Verify robot IP is visible
   - Check if port 3000 is open

## 🚀 Quick Fix Commands

**One-liner firewall fix:**
```bash
sudo ufw allow 3000 && sudo ufw allow 9090
```

**Restart server with network access:**
```bash
# Stop current server (Ctrl+C), then:
HOST=0.0.0.0 npm start
```

**Get current network info:**
```bash
./get-ip.sh
```

## 📞 Still Not Working?

If none of the above works, try:

1. **Mobile Hotspot Test:**
   - Create hotspot on phone
   - Connect robot to phone's hotspot
   - Try accessing from another device

2. **Different Port:**
   ```bash
   HOST=0.0.0.0 PORT=8080 npm start
   # Then try: http://***********:8080
   ```

3. **Network Reset:**
   ```bash
   sudo systemctl restart networking
   # Or reboot both devices
   ```

## 💡 Success Indicators

When working correctly, you should see:
- ✅ Server starts with "Network: http://***********:3000"
- ✅ Phone can ping ***********
- ✅ Phone browser loads the React interface
- ✅ Network info shows in the web interface

---

**Most likely solution: Run `sudo ufw allow 3000` and try again!**
