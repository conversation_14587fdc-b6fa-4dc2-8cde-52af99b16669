#!/bin/bash

# Simple script to get and display the robot's IP address for mobile access

echo "🤖 Robot IP Address Detection"
echo "============================="

# Function to get IP address
get_ip() {
    # Try hostname -I first (most reliable on Ubuntu)
    IP=$(hostname -I | awk '{print $1}' 2>/dev/null)
    
    # Fallback to ip route
    if [ -z "$IP" ]; then
        IP=$(ip route get 1 2>/dev/null | awk '{print $7}' | head -1)
    fi
    
    # Fallback to ifconfig
    if [ -z "$IP" ]; then
        IP=$(ifconfig 2>/dev/null | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1)
    fi
    
    echo "$IP"
}

ROBOT_IP=$(get_ip)

if [ -n "$ROBOT_IP" ]; then
    echo "📡 Robot IP Address: $ROBOT_IP"
    echo ""
    echo "📱 Mobile Access URLs:"
    echo "  Web Interface: http://$ROBOT_IP:3000"
    echo "  ROS Bridge:    ws://$ROBOT_IP:9090"
    echo ""
    echo "📋 Quick Setup Commands:"
    echo "  1. Start ROS Bridge: roslaunch rosbridge_server rosbridge_websocket.launch"
    echo "  2. Start Web App:    npm start"
    echo "  3. Open on phone:    http://$ROBOT_IP:3000"
else
    echo "❌ Could not detect IP address"
    echo "   Please check network connection"
    echo "   Or manually find IP with: ip addr show"
fi
