import React, { createContext, useState, useEffect } from 'react';
import ROS<PERSON><PERSON> from 'roslib';

// Create a context for ROS connection
export const RosContext = createContext(null);

const RosConnection = ({ children, url = 'ws://localhost:9090' }) => {
  const [ros, setRos] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('Disconnected');

  useEffect(() => {
    // Initialize ROS connection
    const newRos = new ROSLIB.Ros({
      url: url
    });

    // Set up event listeners
    newRos.on('connection', () => {
      console.log('Connected to websocket server.');
      setIsConnected(true);
      setConnectionStatus('Connected');
    });

    newRos.on('error', (error) => {
      console.error('Error connecting to websocket server:', error);
      setIsConnected(false);
      setConnectionStatus('Error');
    });

    newRos.on('close', () => {
      console.log('Connection to websocket server closed.');
      setIsConnected(false);
      setConnectionStatus('Disconnected');
    });

    // Set the ROS instance
    setRos(newRos);

    // Clean up on unmount
    return () => {
      if (newRos) {
        newRos.close();
      }
    };
  }, [url]);

  // Provide the ROS instance and connection status to children
  return (
    <RosContext.Provider value={{ ros, isConnected, connectionStatus }}>
      {children}
    </RosContext.Provider>
  );
};

export default RosConnection;
