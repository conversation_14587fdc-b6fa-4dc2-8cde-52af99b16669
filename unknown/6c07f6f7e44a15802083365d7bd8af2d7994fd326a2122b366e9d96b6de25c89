import React, { useContext, useEffect, useState } from 'react';
import ROS<PERSON><PERSON> from 'roslib';
import { RosContext } from './RosConnection';
import './RobotStatus.css';

const RobotStatus = () => {
  const { ros, isConnected } = useContext(RosContext);
  const [odomData, setOdomData] = useState(null);
  const [filteredOdomData, setFilteredOdomData] = useState(null);
  const [cmdVelData, setCmdVelData] = useState(null);

  useEffect(() => {
    if (!isConnected || !ros) return;

    // Subscribe to odometry
    const odomSubscriber = new ROSLIB.Topic({
      ros: ros,
      name: '/odom',
      messageType: 'nav_msgs/Odometry'
    });

    odomSubscriber.subscribe((message) => {
      setOdomData(message);
    });

    // Subscribe to filtered odometry
    const filteredOdomSubscriber = new ROSLIB.Topic({
      ros: ros,
      name: '/odometry/filtered',
      messageType: 'nav_msgs/Odometry'
    });

    filteredOdomSubscriber.subscribe((message) => {
      setFilteredOdomData(message);
    });

    // Subscribe to cmd_vel
    const cmdVelSubscriber = new ROSLIB.Topic({
      ros: ros,
      name: '/cmd_vel',
      messageType: 'geometry_msgs/Twist'
    });

    cmdVelSubscriber.subscribe((message) => {
      setCmdVelData(message);
    });

    // Clean up on unmount
    return () => {
      odomSubscriber.unsubscribe();
      filteredOdomSubscriber.unsubscribe();
      cmdVelSubscriber.unsubscribe();
    };
  }, [ros, isConnected]);

  // Helper function to format position data
  const formatPosition = (position) => {
    if (!position) return 'N/A';
    return `X: ${position.x.toFixed(2)}, Y: ${position.y.toFixed(2)}, Z: ${position.z.toFixed(2)}`;
  };

  // Helper function to format orientation as Euler angles
  const formatOrientation = (orientation) => {
    if (!orientation) return 'N/A';
    
    // Convert quaternion to Euler angles
    const quaternion = new ROSLIB.Quaternion(orientation);
    const roll = Math.atan2(2 * (quaternion.w * quaternion.x + quaternion.y * quaternion.z),
                           1 - 2 * (quaternion.x * quaternion.x + quaternion.y * quaternion.y));
    const pitch = Math.asin(2 * (quaternion.w * quaternion.y - quaternion.z * quaternion.x));
    const yaw = Math.atan2(2 * (quaternion.w * quaternion.z + quaternion.x * quaternion.y),
                          1 - 2 * (quaternion.y * quaternion.y + quaternion.z * quaternion.z));
    
    // Convert to degrees
    const rollDeg = roll * (180 / Math.PI);
    const pitchDeg = pitch * (180 / Math.PI);
    const yawDeg = yaw * (180 / Math.PI);
    
    return `Roll: ${rollDeg.toFixed(2)}°, Pitch: ${pitchDeg.toFixed(2)}°, Yaw: ${yawDeg.toFixed(2)}°`;
  };

  // Helper function to format velocity data
  const formatVelocity = (twist) => {
    if (!twist) return 'N/A';
    return `Linear: ${twist.linear.x.toFixed(2)} m/s, Angular: ${twist.angular.z.toFixed(2)} rad/s`;
  };

  return (
    <div className="robot-status-container">
      <h2>Robot Status</h2>
      
      <div className="status-section">
        <h3>Command Velocity</h3>
        <p>{cmdVelData ? formatVelocity(cmdVelData) : 'No data'}</p>
      </div>
      
      <div className="status-section">
        <h3>Odometry</h3>
        {odomData ? (
          <>
            <p><strong>Position:</strong> {formatPosition(odomData.pose.pose.position)}</p>
            <p><strong>Orientation:</strong> {formatOrientation(odomData.pose.pose.orientation)}</p>
            <p><strong>Velocity:</strong> {formatVelocity(odomData.twist.twist)}</p>
          </>
        ) : (
          <p>No odometry data</p>
        )}
      </div>
      
      <div className="status-section">
        <h3>Filtered Odometry</h3>
        {filteredOdomData ? (
          <>
            <p><strong>Position:</strong> {formatPosition(filteredOdomData.pose.pose.position)}</p>
            <p><strong>Orientation:</strong> {formatOrientation(filteredOdomData.pose.pose.orientation)}</p>
            <p><strong>Velocity:</strong> {formatVelocity(filteredOdomData.twist.twist)}</p>
          </>
        ) : (
          <p>No filtered odometry data</p>
        )}
      </div>
    </div>
  );
};

export default RobotStatus;
