#!/usr/bin/env python
import rospy
from sensor_msgs.msg import Imu
import tf.transformations

def imu_callback(msg):
    # Create a new IMU message with corrected orientation
    corrected_msg = Imu()
    corrected_msg.header = msg.header
    
    # Normalize the quaternion (assuming the values are valid but unnormalized)
    # If they're raw sensor values, you'll need custom conversion logic
    quat = [msg.orientation.x, msg.orientation.y, msg.orientation.z, msg.orientation.w]
    norm = sum([x*x for x in quat])**0.5
    
    if norm > 0:
        corrected_msg.orientation.x = quat[0]/norm
        corrected_msg.orientation.y = quat[1]/norm
        corrected_msg.orientation.z = quat[2]/norm
        corrected_msg.orientation.w = quat[3]/norm
    
    # Copy other data
    corrected_msg.angular_velocity = msg.angular_velocity
    corrected_msg.linear_acceleration = msg.linear_acceleration
    
    # Set proper covariance values (replace -1 with actual values)
    # Using moderate default values
    if msg.orientation_covariance[0] < 0:
        corrected_msg.orientation_covariance = [0.01, 0, 0, 0, 0.01, 0, 0, 0, 0.01]
    else:
        corrected_msg.orientation_covariance = msg.orientation_covariance
        
    if msg.angular_velocity_covariance[0] < 0:
        corrected_msg.angular_velocity_covariance = [0.01, 0, 0, 0, 0.01, 0, 0, 0, 0.01]
    else:
        corrected_msg.angular_velocity_covariance = msg.angular_velocity_covariance
        
    if msg.linear_acceleration_covariance[0] < 0:
        corrected_msg.linear_acceleration_covariance = [0.04, 0, 0, 0, 0.04, 0, 0, 0, 0.04]
    else:
        corrected_msg.linear_acceleration_covariance = msg.linear_acceleration_covariance
    
    # Publish corrected message
    corrected_pub.publish(corrected_msg)

if __name__ == '__main__':
    rospy.init_node('imu_corrector')
    
    # Create publisher for corrected IMU data
    corrected_pub = rospy.Publisher('/imu/data_corrected', Imu, queue_size=10)
    
    # Subscribe to raw IMU data
    rospy.Subscriber('/imu/data', Imu, imu_callback)
    
    rospy.spin()
