<?xml version="1.0" ?>
<robot name="Robot" xmlns:xacro="http://www.ros.org/wiki/xacro" >

<xacro:property name="body_color" value="Gazebo/Silver" />





<gazebo>
    <plugin name="p3d_base_controller" filename="libgazebo_ros_p3d.so">
        <alwaysOn>true</alwaysOn>
        <updateRate>50.0</updateRate>
        <bodyName>base_link</bodyName>
        <topicName>pose</topicName>
        <gaussianNoise>0.01</gaussianNoise>
        <frameName>world</frameName>
        <xyzOffsets>0 0 0</xyzOffsets>
        <rpyOffsets>0 0 0</rpyOffsets>
    </plugin>
  </gazebo>
  
  <gazebo reference="imu_link">
    <gravity>true</gravity>
    <sensor name="imu_sensor" type="imu">
      <always_on>true</always_on>
      <update_rate>100</update_rate>
      <visualize>true</visualize>
      <topic>__default_topic__</topic>
      <plugin filename="libgazebo_ros_imu_sensor.so" name="imu_plugin">
        <topicName>imu_gazebo/data</topicName>
        <bodyName>imu_gazebo_link</bodyName>
        <updateRateHZ>100.0</updateRateHZ>
        <gaussianNoise>0.0</gaussianNoise>
        <xyzOffset>0 0 0</xyzOffset>
        <rpyOffset>0 0 0</rpyOffset>
        <frameName>imu_gazebo_link</frameName>
      </plugin>
      <pose>0 0 0 0 0 0</pose>
    </sensor>
  </gazebo>
  
  


<gazebo reference="base_link">
  <material>${body_color}</material>
  <mu1>1</mu1>
  <mu2>1</mu2>
  <selfCollide>true</selfCollide>
  <gravity>true</gravity>
</gazebo>

<gazebo reference="tireBL_1">
  <material>${body_color}</material>
  <mu1>1</mu1>
  <mu2>1</mu2>
  <selfCollide>true</selfCollide>
</gazebo>

<gazebo reference="tireFL_1">
  <material>${body_color}</material>
  <mu1>1</mu1>
  <mu2>1</mu2>
  <selfCollide>true</selfCollide>
</gazebo>

<gazebo reference="tireFR_1">
  <material>${body_color}</material>
  <mu1>1</mu1>
  <mu2>1</mu2>
  <selfCollide>true</selfCollide>
</gazebo>

<gazebo reference="tireBR_1">
  <material>${body_color}</material>
  <mu1>1</mu1>
  <mu2>1</mu2>
  <selfCollide>true</selfCollide>
</gazebo>

</robot>
