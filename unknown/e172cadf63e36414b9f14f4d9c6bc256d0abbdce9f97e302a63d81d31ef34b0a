#!/bin/bash

# <PERSON>ript to install Kinect v1 camera packages for ROS using OpenNI1

echo "Installing Kinect v1 camera packages for ROS using OpenNI1..."

# Update package lists
sudo apt-get update

# Install OpenNI1 and related packages
ROS_DISTRO=$(rosversion -d)
sudo apt-get install -y ros-$ROS_DISTRO-openni-camera ros-$ROS_DISTRO-openni-launch

# Install depth image to laserscan package for navigation
sudo apt-get install -y ros-$ROS_DISTRO-depthimage-to-laserscan

echo "Installation complete!"
echo "You can now use the Kinect v1 camera with ROS using OpenNI1."
echo "To test the camera, run: roslaunch openni_launch openni.launch"
echo "To view the depth image, run: rqt_image_view /camera/depth/image_raw"
