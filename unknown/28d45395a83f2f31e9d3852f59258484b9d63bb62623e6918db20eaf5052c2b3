<?xml version="1.0"?>
<package format="2">
  <name>realsense_gazebo_plugin</name>
  <version>1.1.0</version>
  <description>Intel RealSense D435 Gazebo plugin package</description>
  
  <maintainer email="sergey.dorod<PERSON><EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON>ah-<PERSON><PERSON></maintainer>
  <maintainer email="<EMAIL>">Adria Roig</maintainer>

  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON><PERSON></author>

  <license>Apache 2.0</license>

  <url type="website">http://www.ros.org/wiki/RealSense</url>
  <url type="bugtracker">https://github.com/intel-ros/realsense/issues</url>

  <buildtool_depend>catkin</buildtool_depend>

  <depend>gazebo_ros</depend>
  <depend>roscpp</depend>
  <depend>sensor_msgs</depend>
  <depend>image_transport</depend>
  <depend>camera_info_manager</depend>

  <export>
    <gazebo_ros
      plugin_path="${prefix}/lib"
      gazebo_media_path="${prefix}"
      gazebo_model_path="${prefix}/models"
      />
  </export>
</package>
