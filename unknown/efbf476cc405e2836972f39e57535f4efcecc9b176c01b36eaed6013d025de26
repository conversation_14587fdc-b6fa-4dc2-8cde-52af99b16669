<launch>
    <!-- This launch file runs the full simulation without RVIZ on Device 1 (robot) -->
    
    <!-- Launch Gazebo -->
    <include file="$(find weednix_launch)/launch/gazebo_simulation.launch" />

    <!-- Launch EKF -->
    <node pkg="robot_localization" type="ekf_localization_node" name="ekf_filter_node" output="screen">
        <rosparam command="load" file="$(find weednix_launch)/config/ekf_simualtion.yaml" />
    </node>

    <!-- Launch row crop follower -->
    <node pkg="visual_servoing" type="row_crop_follower.py" name="row_crop_follower" output="screen" launch-prefix="python3">
        <!-- Set image topic for Kinect camera -->
        <param name="image_topic" value="/camera_sim/color/image_raw" />

        <!-- Enable tuning mode to show all debugging windows -->
        <param name="following_only" value="false" />
        <param name="show_frames" value="true" />
        <param name="tuning_mode" value="false" />

  </node>
  
    <!-- Launch path publisher -->
    <node name="path_publisher" pkg="visual_servoing" type="path_publisher" output="screen" />

    <!-- Note: RVIZ is intentionally not included as it will run on Device 2 -->
</launch>
