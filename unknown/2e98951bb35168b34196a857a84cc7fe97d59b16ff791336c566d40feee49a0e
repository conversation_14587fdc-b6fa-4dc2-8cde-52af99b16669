<sdf version='1.6'>
  <world name='default'>
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose frame=''>0 0 10 0 -0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.1 0.1 0.1 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 0.5 -1</direction>
    </light>
    <model name='ground_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>100</mu>
                <mu2>50</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode/>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
    </model>
    <gravity>0 0 -9.8</gravity>
    <magnetic_field>6e-06 2.3e-05 -4.2e-05</magnetic_field>
    <atmosphere type='adiabatic'/>
    <physics name='default_physics' default='0' type='ode'>
      <max_step_size>0.001</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>1000</real_time_update_rate>
    </physics>
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.7 0.7 1</background>
      <shadows>1</shadows>
    </scene>
    <spherical_coordinates>
      <surface_model>EARTH_WGS84</surface_model>
      <latitude_deg>0</latitude_deg>
      <longitude_deg>0</longitude_deg>
      <elevation>0</elevation>
      <heading_deg>0</heading_deg>
    </spherical_coordinates>
    <model name='mud_box'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>8 10 0.2</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual_1'>
          <pose frame=''>-2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_2'>
          <pose frame=''>2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_3'>
          <pose frame=''>2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_4'>
          <pose frame=''>-2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose frame=''>-6.13192 4.89997 0 0 -0 0</pose>
    </model>
    <model name='mud_box_0'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>8 10 0.2</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual_1'>
          <pose frame=''>-2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_2'>
          <pose frame=''>2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_3'>
          <pose frame=''>2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_4'>
          <pose frame=''>-2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose frame=''>1.90734 4.83104 0 0 -0 0</pose>
    </model>
    <model name='mud_box_1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>8 10 0.2</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual_1'>
          <pose frame=''>-2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_2'>
          <pose frame=''>2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_3'>
          <pose frame=''>2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_4'>
          <pose frame=''>-2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose frame=''>-6.12792 -5.13998 0 0 -0 0</pose>
    </model>
    <model name='mud_box_2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>8 10 0.2</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual_1'>
          <pose frame=''>-2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_2'>
          <pose frame=''>2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_3'>
          <pose frame=''>2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_4'>
          <pose frame=''>-2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose frame=''>1.85975 -5.14192 0 0 -0 0</pose>
    </model>
    <state world_name='default'>
      <sim_time>1453 379000000</sim_time>
      <real_time>112 236889786</real_time>
      <wall_time>1555192438 111534923</wall_time>
      <iterations>112023</iterations>
      <model name='big_plant'>
        <pose frame=''>-4.93132 -0.19799 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>-4.93132 -0.19799 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_19'>
        <pose frame=''>-5.87269 -0.155578 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>-5.87269 -0.155578 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone'>
        <pose frame=''>-4.12906 -0.109036 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>-4.12906 -0.109036 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_0'>
        <pose frame=''>-3.89196 -0.157101 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>-3.89196 -0.157101 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_10'>
        <pose frame=''>3.10853 0.123827 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>3.10853 0.123827 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_11'>
        <pose frame=''>3.63047 0.120824 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>3.63047 0.120824 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_12'>
        <pose frame=''>4.20638 0.049733 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>4.20638 0.049733 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_13'>
        <pose frame=''>4.86191 0.241713 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>4.86191 0.241713 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_14'>
        <pose frame=''>5.71837 0.091423 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>5.71837 0.091423 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_15'>
        <pose frame=''>6.96631 0.247548 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>6.96631 0.247548 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_16'>
        <pose frame=''>7.97895 0.101523 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>7.97895 0.101523 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_17'>
        <pose frame=''>7.72023 0.024822 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>7.72023 0.024822 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_18'>
        <pose frame=''>8.67369 0.138078 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>8.67369 0.138078 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_19'>
        <pose frame=''>9.71692 0.149412 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>9.71692 0.149412 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_2'>
        <pose frame=''>-2.56084 -0.129029 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>-2.56084 -0.129029 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_20'>
        <pose frame=''>10.76 0.160802 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>10.76 0.160802 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_21'>
        <pose frame=''>12.2344 0.141852 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>12.2344 0.141852 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_4'>
        <pose frame=''>-1.20437 -0.049812 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>-1.20437 -0.049812 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_5'>
        <pose frame=''>-0.693027 0.108975 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>-0.693027 0.108975 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_6'>
        <pose frame=''>-0.021989 -0.023329 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>-0.021989 -0.023329 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_7'>
        <pose frame=''>0.826988 0.152011 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>0.826988 0.152011 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_8'>
        <pose frame=''>1.0375 0.078023 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>1.0375 0.078023 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='big_plant_clone_9'>
        <pose frame=''>2.34707 0.156808 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='big_plant_22::link_0'>
          <pose frame=''>2.34707 0.156808 0.111949 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='ground_plane'>
        <pose frame=''>0 0 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='mud_box'>
        <pose frame=''>-6.12765 4.83622 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-6.12765 4.83622 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='mud_box_0'>
        <pose frame=''>1.86767 4.83104 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>1.86767 4.83104 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='mud_box_0_clone'>
        <pose frame=''>9.85757 -5.14216 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>9.85757 -5.14216 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='mud_box_0_clone_clone'>
        <pose frame=''>17.7661 -5.12084 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>17.7661 -5.12084 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='mud_box_0_clone_clone_0'>
        <pose frame=''>17.8432 4.79546 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>17.8432 4.79546 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='mud_box_0_clone_clone_1'>
        <pose frame=''>-14.1276 -5.15209 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-14.1276 -5.15209 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='mud_box_0_clone_clone_2'>
        <pose frame=''>-14.1258 4.84756 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-14.1258 4.84756 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='mud_box_1'>
        <pose frame=''>-6.12792 -5.13998 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-6.12792 -5.13998 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='mud_box_2'>
        <pose frame=''>1.85975 -5.14192 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>1.85975 -5.14192 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='mud_box_3'>
        <pose frame=''>9.84986 4.82173 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>9.84986 4.82173 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>

      <light name='sun'>
        <pose frame=''>0 0 10 0 -0 0</pose>
      </light>
    </state>
    <gui fullscreen='0'>
      <camera name='user_camera'>
        <pose frame=''>-0.867906 -2.27965 3.20233 0 1.05662 1.38675</pose>
        <view_controller>orbit</view_controller>
        <projection_type>perspective</projection_type>
      </camera>
    </gui>
    <model name='mud_box_0_clone'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>8 10 0.2</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual_1'>
          <pose frame=''>-2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_2'>
          <pose frame=''>2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_3'>
          <pose frame=''>2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_4'>
          <pose frame=''>-2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose frame=''>9.81681 -5.15165 0 0 -0 0</pose>
    </model>
    <model name='mud_box_0_clone_clone'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>8 10 0.2</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual_1'>
          <pose frame=''>-2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_2'>
          <pose frame=''>2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_3'>
          <pose frame=''>2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_4'>
          <pose frame=''>-2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose frame=''>17.8709 -5.12525 0 0 -0 0</pose>
    </model>
    <model name='mud_box_0_clone_clone_0'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>8 10 0.2</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual_1'>
          <pose frame=''>-2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_2'>
          <pose frame=''>2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_3'>
          <pose frame=''>2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_4'>
          <pose frame=''>-2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose frame=''>17.8432 4.79546 0 0 -0 0</pose>
    </model>
    <model name='mud_box_0_clone_clone_1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>8 10 0.2</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual_1'>
          <pose frame=''>-2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_2'>
          <pose frame=''>2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_3'>
          <pose frame=''>2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_4'>
          <pose frame=''>-2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose frame=''>-14.1276 -5.15209 0 0 -0 0</pose>
    </model>
    <model name='mud_box_0_clone_clone_2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>8 10 0.2</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual_1'>
          <pose frame=''>-2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_2'>
          <pose frame=''>2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_3'>
          <pose frame=''>2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_4'>
          <pose frame=''>-2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose frame=''>-14.0968 4.86277 0 0 -0 0</pose>
    </model>

    <model name='big_plant_19'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>-5.87269 -0.155578 0 0 -0 0</pose>
    </model>
    <model name='big_plant'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>-4.93132 -0.19799 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>-4.12906 -0.109036 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_0'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>-3.89196 -0.157101 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_2'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>-2.56084 -0.129029 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_4'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>-1.20437 -0.049812 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_5'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>-0.693027 0.108975 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_6'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>-0.021989 -0.023329 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_7'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>0.826988 0.152011 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_8'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>1.0375 0.078023 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_9'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>2.34707 0.156808 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_10'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>3.10853 0.123827 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_11'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>3.63047 0.120824 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_12'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>4.20638 0.049733 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_13'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>4.86191 0.241713 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_14'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>5.71837 0.091423 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_15'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>6.96631 0.247548 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_16'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>7.97895 0.101523 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_17'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>7.72023 0.024822 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_18'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>8.67369 0.138078 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_19'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>9.71692 0.149412 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_20'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>10.76 0.160802 0 0 -0 0</pose>
    </model>
    <model name='big_plant_clone_21'>
      <link name='big_plant_22::link_0'>
        <pose frame=''>0 0 0.111949 0 -0 0</pose>
        <inertial>
          <mass>1e-08</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose frame=''>0 0 0 0 -0 0</pose>
        </inertial>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <visual name='visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://big_plant/mesh/big_plant.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <material>
            <lighting>1</lighting>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>0.3 0.3 0.3 1</ambient>
            <diffuse>0.7 0.7 0.7 1</diffuse>
            <specular>0.01 1 0.01 1</specular>
            <emissive>0 1 0 1</emissive>
            <shader type='vertex'>
              <normal_map>__default__</normal_map>
            </shader>
          </material>
          <cast_shadows>1</cast_shadows>
          <transparency>0</transparency>
        </visual>
        <gravity>1</gravity>
      </link>
      <static>1</static>
      <allow_auto_disable>1</allow_auto_disable>
      <pose frame=''>12.2344 0.141852 0 0 -0 0</pose>
    </model>
    <model name='mud_box_3'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>8 10 0.2</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual_1'>
          <pose frame=''>-2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_2'>
          <pose frame=''>2 2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_3'>
          <pose frame=''>2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_4'>
          <pose frame=''>-2 -2.5 0 0 -0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose frame=''>9.84986 4.82173 0 0 -0 0</pose>
    </model>
  </world>
</sdf>

