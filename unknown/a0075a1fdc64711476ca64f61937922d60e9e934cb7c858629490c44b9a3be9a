# USB Serial Device Identification and udev Rules Setup

This guide outlines how to identify USB serial devices and create persistent symbolic links using udev rules, particularly for an Arduino and an IMU device.

## 1. Identify USB Device Information

To get detailed information about a connected USB device (e.g., `/dev/ttyUSB0`):


udevadm info -a -n /dev/ttyUSB0


## Step 2: Create udev Rule File

sudo nano /etc/udev/rules.d/99-usb-serial.rules
#Add the Following Rules:
----------------------------------------------

# GPS Device (mt3339)
SUBSYSTEM=="tty", ATTRS{idVendor}=="10c4", ATTRS{idProduct}=="ea60", SYMLINK+="ttyUSB_gps"

#For Arduino (motion)
SUBSYSTEM=="tty", ATTRS{idVendor}=="1a86", ATTRS{idProduct}=="7523", SYMLINK+=">

#for IMU
SUBSYSTEM=="tty", ATTRS{idVendor}=="067b", ATTRS{idProduct}=="2303", ATTRS{prod>


--------------------------------------------------------

##Step 3: Reload and Trigger udev Rules


sudo udevadm control --reload-rules
sudo udevadm trigger

##Step 4: Reconnect USB Devices


ls -l /dev/ttyUSB_arduino

##Step 5: Update ROS Launch File

like     <param name="port" value="/dev/ttyUSB_imu" />


