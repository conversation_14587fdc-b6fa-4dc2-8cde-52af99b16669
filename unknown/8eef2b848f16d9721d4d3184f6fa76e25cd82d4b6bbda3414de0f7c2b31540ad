<launch>

    <!-- Launch the BNO055 IMU driver -->
    <include file="$(find ros_imu_bno055)/launch/imu.launch">
        <arg name="serial_port" value="/dev/ttyUSB_imu_bno055" />
        <arg name="frame_id" value="imu_link" />
        <arg name="operation_mode" value="IMU" />
        <arg name="oscillator" value="INTERNAL" />
        <arg name="reset_orientation" value="true" />
        <arg name="frequency" value="50" /> 
        <arg name="use_magnetometer" value="false" />
        <arg name="use_temperature" value="false" /> 
    </include>

    <!-- Launch your custom imu_corrector node -->
    <node pkg="weednix_sensors" type="imu_corrector.py" name="imu_corrector" output="screen" />

</launch>

