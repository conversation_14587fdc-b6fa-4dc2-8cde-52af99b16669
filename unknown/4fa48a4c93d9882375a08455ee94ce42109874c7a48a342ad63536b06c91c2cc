#!/bin/bash

# Weednix Robot Stop Script
# This script stops all robot services cleanly

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
LOG_DIR="$HOME/weednix_ws/logs"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to stop processes by PID file
stop_by_pid_file() {
    local pid_file="$1"
    local service_name="$2"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            print_status "Stopping $service_name (PID: $pid)..."
            kill "$pid" 2>/dev/null || true
            sleep 2
            
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                print_warning "Force killing $service_name..."
                kill -9 "$pid" 2>/dev/null || true
            fi
            
            print_success "$service_name stopped"
        else
            print_warning "$service_name was not running"
        fi
        rm -f "$pid_file"
    else
        print_warning "No PID file found for $service_name"
    fi
}

# Function to stop processes by pattern
stop_by_pattern() {
    local pattern="$1"
    local service_name="$2"
    
    local pids=$(pgrep -f "$pattern" 2>/dev/null || true)
    if [ -n "$pids" ]; then
        print_status "Stopping $service_name processes..."
        pkill -f "$pattern" 2>/dev/null || true
        sleep 2
        
        # Force kill if still running
        local remaining_pids=$(pgrep -f "$pattern" 2>/dev/null || true)
        if [ -n "$remaining_pids" ]; then
            print_warning "Force killing remaining $service_name processes..."
            pkill -9 -f "$pattern" 2>/dev/null || true
        fi
        
        print_success "$service_name stopped"
    else
        print_warning "$service_name was not running"
    fi
}

# Main execution
main() {
    echo ""
    echo "=========================================="
    echo -e "${BLUE}🛑 STOPPING WEEDNIX ROBOT SERVICES${NC}"
    echo "=========================================="
    echo ""
    
    # Stop services using PID files if they exist
    if [ -d "$LOG_DIR" ]; then
        stop_by_pid_file "$LOG_DIR/robot_launch.pid" "Robot Launch"
        stop_by_pid_file "$LOG_DIR/simulation_launch.pid" "Simulation Launch"
        stop_by_pid_file "$LOG_DIR/rosbridge.pid" "ROS Bridge"
        stop_by_pid_file "$LOG_DIR/webserver.pid" "Web Server"
    fi
    
    # Stop any remaining processes by pattern matching
    print_status "Cleaning up any remaining processes..."
    
    stop_by_pattern "roslaunch.*device1_" "ROS Launch Files"
    stop_by_pattern "roslaunch.*rosbridge_websocket" "ROS Bridge"
    stop_by_pattern "npm.*start" "NPM Start"
    stop_by_pattern "react-scripts start" "React Scripts"
    
    # Stop any remaining ROS nodes
    stop_by_pattern "roscore" "ROS Core"
    stop_by_pattern "rosmaster" "ROS Master"
    
    echo ""
    print_success "All robot services have been stopped!"
    echo ""
    echo -e "${YELLOW}💡 Note:${NC} Gazebo GUI may still be open - close it manually if needed"
    echo ""
}

# Run main function
main "$@"
