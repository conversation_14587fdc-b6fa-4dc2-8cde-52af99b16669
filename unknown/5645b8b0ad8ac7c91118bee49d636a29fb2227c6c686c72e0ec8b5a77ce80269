<launch>
  <!-- Launch file for simulation with visual servoing -->
  
  <!-- Launch Gazebo simulation -->
  <include file="$(find weednix_launch)/launch/gazebo_simulation.launch" />
  
  <!-- Launch visual servoing node -->
  <node pkg="visual_servoing" type="row_crop_follower.py" name="row_crop_follower" output="screen" launch-prefix="python3">
    <!-- Configure visual servoing parameters for simulation -->
    <param name="width" value="640" />
    <param name="height" value="480" />
    <param name="ex_Xc" value="320" />
    <param name="ex_Yc" value="240" />
    <param name="nh_L" value="150" />
    <param name="nh_H" value="250" />
    <param name="nh_offset" value="200" />
    <param name="steering_dir" value="1" />
    <param name="following_only" value="true" />
    <param name="show_frames" value="true" />
    <param name="image_topic" value="/camera_sim/color/image_raw" />
  </node>
  
  <!-- Launch path publisher -->
  <node name="path_publisher" pkg="visual_servoing" type="path_publisher" output="screen" />
  
  <!-- Optional: Add visualization in RViz -->
  <arg name="rviz" default="true"/>
  <group if="$(arg rviz)">
    <include file="$(find weednix_launch)/launch/rviz_launch.launch" />
  </group>
</launch>
