#!/usr/bin/env python

import rospy
from math import sin, cos

from geometry_msgs.msg import Quaternion, Point32
from nav_msgs.msg import Odometry
from tf.broadcaster import TransformBroadcaster

#############################################################################
class DiffTf:
#############################################################################

    def __init__(self):
        rospy.init_node("diff_tf")
        self.nodename = rospy.get_name()
        rospy.loginfo("-I- %s started" % self.nodename)
        
        self.rate = rospy.get_param('~rate', 10.0)
        self.ticks_meter = float(rospy.get_param('ticks_meter', 38545.34))
        self.base_width = float(rospy.get_param('~base_width', 0.6))
        
        self.base_frame_id = rospy.get_param('~base_frame_id', 'base_link')
        self.odom_frame_id = rospy.get_param('~odom_frame_id', 'odom')
        
        self.encoder_min = rospy.get_param('encoder_min', -2147483648)
        self.encoder_max = rospy.get_param('encoder_max', 2147483648)
        self.encoder_low_wrap = rospy.get_param('wheel_low_wrap', (self.encoder_max - self.encoder_min) * 0.3 + self.encoder_min)
        self.encoder_high_wrap = rospy.get_param('wheel_high_wrap', (self.encoder_max - self.encoder_min) * 0.7 + self.encoder_min)
 
        self.t_delta = rospy.Duration(1.0 / self.rate)
        self.t_next = rospy.Time.now() + self.t_delta
        
        self.enc_left = None
        self.enc_right = None
        self.left = 0
        self.right = 0
        self.lmult = 0
        self.rmult = 0
        self.prev_lencoder = 0
        self.prev_rencoder = 0
        self.x = 0
        self.y = 0
        self.th = 0
        self.dx = 0
        self.dr = 0
        self.then = rospy.Time.now()
        
        # Subscribe to encoder_counts topic
        rospy.Subscriber("/encoder_counts", Point32, self.encoderCallback)

        self.odomPub = rospy.Publisher("odom", Odometry, queue_size=10)
        self.odomBroadcaster = TransformBroadcaster()
        
    def spin(self):
        r = rospy.Rate(self.rate)
        while not rospy.is_shutdown():
            self.update()
            r.sleep()
       
    def update(self):
        now = rospy.Time.now()
        if now > self.t_next:
            elapsed = now - self.then
            self.then = now
            elapsed = elapsed.to_sec()

            if self.enc_left is None:
                d_left = 0
                d_right = 0
            else:
                d_left = (self.left - self.enc_left) / self.ticks_meter
                d_right = (self.right - self.enc_right) / self.ticks_meter
            self.enc_left = self.left
            self.enc_right = self.right

            d = (d_left + d_right) / 2
            th = (d_right - d_left) / self.base_width
            self.dx = d / elapsed
            self.dr = th / elapsed

            if d != 0:
                x = cos(th) * d
                y = -sin(th) * d
                self.x += cos(self.th) * x - sin(self.th) * y
                self.y += sin(self.th) * x + cos(self.th) * y
            if th != 0:
                self.th += th

            quaternion = Quaternion()
            quaternion.x = 0.0
            quaternion.y = 0.0
            quaternion.z = sin(self.th / 2)
            quaternion.w = cos(self.th / 2)
            # self.odomBroadcaster.sendTransform(
            #     (self.x, self.y, 0),
            #     (quaternion.x, quaternion.y, quaternion.z, quaternion.w),
            #     rospy.Time.now(),
            #     self.base_frame_id,
            #     self.odom_frame_id
            # )

            odom = Odometry()
            odom.header.stamp = now
            odom.header.frame_id = self.odom_frame_id
            odom.pose.pose.position.x = self.x
            odom.pose.pose.position.y = self.y
            odom.pose.pose.position.z = 0
            odom.pose.pose.orientation = quaternion
            odom.child_frame_id = self.base_frame_id
            odom.twist.twist.linear.x = self.dx
            odom.twist.twist.linear.y = 0
            odom.twist.twist.angular.z = self.dr
            self.odomPub.publish(odom)

    def encoderCallback(self, msg):
        r_enc = int(msg.x)
        l_enc = int(msg.y)

        if r_enc < self.encoder_low_wrap and self.prev_rencoder > self.encoder_high_wrap:
            self.rmult += 1
        if r_enc > self.encoder_high_wrap and self.prev_rencoder < self.encoder_low_wrap:
            self.rmult -= 1

        if l_enc < self.encoder_low_wrap and self.prev_lencoder > self.encoder_high_wrap:
            self.lmult += 1
        if l_enc > self.encoder_high_wrap and self.prev_lencoder < self.encoder_low_wrap:
            self.lmult -= 1

        self.right = 1.0 * (r_enc + self.rmult * (self.encoder_max - self.encoder_min))
        self.left = 1.0 * (l_enc + self.lmult * (self.encoder_max - self.encoder_min))

        self.prev_rencoder = r_enc
        self.prev_lencoder = l_enc

if __name__ == '__main__':
    diffTf = DiffTf()
    diffTf.spin()
