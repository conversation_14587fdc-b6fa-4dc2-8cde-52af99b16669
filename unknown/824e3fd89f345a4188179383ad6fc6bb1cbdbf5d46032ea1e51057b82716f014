Robot_controller:
  # Publish all joint states -----------------------------------
  joint_state_controller:
    type: joint_state_controller/JointStateController
    publish_rate: 50

  # Position Controllers --------------------------------------
  Revolute_5_position_controller:
    type: effort_controllers/JointPositionController
    joint: Revolute_5
    pid: {p: 100.0, i: 0.01, d: 10.0}
  Revolute_6_position_controller:
    type: effort_controllers/JointPositionController
    joint: Revolute_6
    pid: {p: 100.0, i: 0.01, d: 10.0}
  Revolute_7_position_controller:
    type: effort_controllers/JointPositionController
    joint: Revolute_7
    pid: {p: 100.0, i: 0.01, d: 10.0}
  Revolute_8_position_controller:
    type: effort_controllers/JointPositionController
    joint: Revolute_8
    pid: {p: 100.0, i: 0.01, d: 10.0}
