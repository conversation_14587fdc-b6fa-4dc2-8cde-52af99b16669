<launch>
  <arg name="model" default="$(find robot_description)/urdf/Robot.xacro"/>
  <arg name="gui" default="true"/>
  <arg name="rvizconfig" default="$(find robot_description)/launch/urdf.rviz"/>
  <param name="robot_description" command="$(find xacro)/xacro $(arg model)"/>
  <param name="use_gui" value="$(arg gui)"/>
  <node name="joint_state_publisher_gui" pkg="joint_state_publisher_gui" type="joint_state_publisher_gui"/>
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher"/>
  <node name="rviz" pkg="rviz" args="-d $(arg rvizconfig)" type="rviz" required="true"/>
</launch>
