#!/usr/bin/env python3

import rospy
import json
import numpy as np
from shapely.geometry import Point, Polygon
from sensor_msgs.msg import NavSatFix
from std_msgs.msg import Bool, String
from geopy.distance import distance

class GeofencingNode:
    def __init__(self):
        rospy.init_node('geofencing_node')

        # Parameters
        self.geojson_file = rospy.get_param('~geojson_file', 'boundaries.geojson')
        self.gps_error_radius = rospy.get_param('~gps_error_radius', 2.0)  # Error radius in meters
        self.gps_topic = rospy.get_param('~gps_topic', '/gps/fix')
        self.update_rate = rospy.get_param('~update_rate', 50.0)  # Hz

        # Load GeoJSON boundary
        self.boundary_polygon = self.load_boundary()

        # Most recent GPS coordinates
        self.current_lat = None
        self.current_lon = None
        self.is_inside = False
        self.distance_to_boundary = float('inf')

        # Publishers
        self.inside_pub = rospy.Publisher('~inside_boundary', Bool, queue_size=1)
        self.status_pub = rospy.Publisher('~boundary_status', String, queue_size=1)

        # Subscribers
        rospy.Subscriber(self.gps_topic, NavSatFix, self.gps_callback)

        # Timer for periodic checks
        self.timer = rospy.Timer(rospy.Duration(1.0/self.update_rate), self.check_boundary)

        rospy.loginfo("Geofencing node initialized with boundary from: %s", self.geojson_file)
        rospy.loginfo("GPS error radius set to: %.2f meters", self.gps_error_radius)
        # Make sure these match between your GPS plugin and geofencing node
        print("GPS plugin reference: {}, {}".format(
            rospy.get_param('/gazebo_ros_gps/referenceLatitude', 'unknown'),
            rospy.get_param('/gazebo_ros_gps/referenceLongitude', 'unknown')))

    def load_boundary(self):
        """Load boundary polygon from GeoJSON file"""
        try:
            with open(self.geojson_file, 'r') as f:
                geojson_data = json.load(f)

            # Extract coordinates from the first feature in the GeoJSON
            # Assuming the GeoJSON contains a polygon feature
            for feature in geojson_data['features']:
                if feature['geometry']['type'] == 'Polygon':
                    coordinates = feature['geometry']['coordinates'][0]  # First polygon, outer ring
                    # Convert to shapely polygon format (convert from [lon, lat] to [lat, lon])
                    polygon_coords = [(coord[1], coord[0]) for coord in coordinates]
                    return Polygon(polygon_coords)

            rospy.logerr("No polygon found in GeoJSON file")
            return None

        except Exception as e:
            rospy.logerr("Failed to load boundary from GeoJSON: %s", str(e))
            return None

    def gps_callback(self, msg):
        """Callback for GPS messages"""
        self.current_lat = msg.latitude
        self.current_lon = msg.longitude

    def get_distance_to_boundary(self, point, polygon):
        """Calculate minimum distance from point to polygon boundary"""
        if polygon.contains(point):
            # Point is inside, calculate distance to nearest edge
            boundary = polygon.exterior
            return boundary.distance(point)
        else:
            # Point is outside
            return -polygon.exterior.distance(point)

    def check_boundary(self, event=None):
        """Check if current position is within boundary, considering GPS error"""
        if self.current_lat is None or self.current_lon is None:
            rospy.logwarn_throttle(10, "No GPS data received yet")
            return

        if self.boundary_polygon is None:
            rospy.logerr_throttle(10, "No valid boundary polygon loaded")
            return

        # Create point from current GPS coordinates
        current_point = Point(self.current_lat, self.current_lon)

        # Calculate distance to boundary (positive if inside, negative if outside)
        self.distance_to_boundary = self.get_distance_to_boundary(current_point, self.boundary_polygon)

        # Consider a point inside only if it's inside by more than the GPS error radius
        # Convert distance to meters first (it's in degrees)
        lat_lon_distance = abs(self.distance_to_boundary)
        meters_distance = self.convert_degree_distance_to_meters(lat_lon_distance, self.current_lat)

        # Point is inside if distance is positive and greater than error radius
        # or if distance is negative but absolute value less than error radius
        if self.distance_to_boundary > 0:
            if meters_distance > self.gps_error_radius:
                self.is_inside = True
                status_msg = f"Inside boundary by {meters_distance:.2f}m (GPS error: {self.gps_error_radius:.2f}m)"
            else:
                self.is_inside = False
                status_msg = f"Near boundary (inside by {meters_distance:.2f}m) - Within GPS error zone"
        else:
            if meters_distance < self.gps_error_radius:
                self.is_inside = True
                status_msg = f"Near boundary (outside by {meters_distance:.2f}m) - Within GPS error zone"
            else:
                self.is_inside = False
                status_msg = f"Outside boundary by {meters_distance:.2f}m (GPS error: {self.gps_error_radius:.2f}m)"

        # Publish results
        self.inside_pub.publish(Bool(self.is_inside))
        self.status_pub.publish(String(status_msg))

        # Log status (throttled to avoid flooding)
        if self.is_inside:
            rospy.loginfo_throttle(5, status_msg)
        else:
            rospy.logwarn_throttle(5, status_msg)

    def convert_degree_distance_to_meters(self, degree_distance, latitude):
        """Convert a distance in degrees to meters at a given latitude"""
        # We need a reference point to calculate distance
        ref_lat = latitude
        ref_lon = 0.0
        target_lon = degree_distance + ref_lon

        # Calculate the distance in meters
        meters = distance(
            (ref_lat, ref_lon),
            (ref_lat, target_lon)
        ).meters

        return meters

if __name__ == '__main__':
    try:
        node = GeofencingNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
