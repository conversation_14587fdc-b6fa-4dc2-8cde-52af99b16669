#!/bin/bash

# <PERSON>ript to install Kinect v1 camera packages for ROS

echo "Installing Kinect v1 camera packages for ROS..."

# Update package lists
case 
sudo apt-get update

# Install libfreenect driver
sudo apt-get install -y libfreenect-dev

# Install ROS freenect packages
ROS_DISTRO=$(rosversion -d)
sudo apt-get install -y ros-$ROS_DISTRO-freenect-launch

# Install depth image to laserscan package for navigation
sudo apt-get install -y ros-$ROS_DISTRO-depthimage-to-laserscan

echo "Installation complete!"
echo "You can now use the Kinect v1 camera with ROS."
echo "To test the camera, run: roslaunch freenect_launch freenect.launch"
echo "To view the depth image, run: rqt_image_view /camera/depth/image_raw"
