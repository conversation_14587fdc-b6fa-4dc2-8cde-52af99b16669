<launch>
  <!-- Launch file for Kinect v1 camera using OpenNI1 -->

  <!-- Launch the Kinect camera using OpenNI1 -->
  <include file="$(find openni_launch)/launch/openni.launch">
    <!-- Optional parameters can be set here -->
    <arg name="depth_registration" value="true" />
    <arg name="rgb_processing" value="true" />
    <arg name="ir_processing" value="false" />
    <arg name="depth_processing" value="true" />
    <arg name="depth_registered_processing" value="true" />
    <arg name="disparity_processing" value="false" />
    <arg name="disparity_registered_processing" value="false" />
  </include>

  <!-- Convert depth image to laser scan for navigation -->
  <node name="depthimage_to_laserscan" pkg="depthimage_to_laserscan" type="depthimage_to_laserscan" output="screen">
    <remap from="image" to="/camera/depth/image_raw"/>
    <remap from="scan" to="/kinect_scan"/>
    <param name="range_min" value="0.45"/>
    <param name="range_max" value="4.0"/>
    <param name="scan_height" value="10"/>
    <param name="output_frame_id" value="camera_depth_optical_frame"/>
  </node>

  <!-- Add a static transform from base_link to the Kinect camera -->
  <node pkg="tf" type="static_transform_publisher" name="kinect_base_link"
        args="0.2 0 0.3 0 0 0 base_link camera_link 100" />
</launch>
