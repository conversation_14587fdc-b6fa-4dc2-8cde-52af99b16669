<launch>
    <!-- This launch file runs RVIZ on Device 2 with robot model support -->

    <!-- Load robot description parameter -->
    <arg name="model" default="$(find robot_description)/urdf/Robot.xacro"/>
    <param name="robot_description" command="$(find xacro)/xacro $(arg model)"/>

    <!-- Add robot_state_publisher to ensure TF frames are available -->


    <!-- Launch RViz with the standard configuration -->
    <include file="$(find weednix_launch)/launch/rviz_launch.launch" />

    <!-- Note: This launch file assumes that ROS_MASTER_URI and ROS_IP
         have been properly configured in .bashrc on both devices.

         The robot_state_publisher here is a backup to ensure TF frames are available.
         The actual joint states will come from Device 1 (robot). -->
</launch>
