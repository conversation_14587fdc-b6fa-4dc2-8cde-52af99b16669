<launch>

  <include file="$(find gazebo_ros)/launch/empty_world.launch">
    <arg name="world_name" value="$(find robot_description)/worlds/farmWith1CropRow.world"/>
    <arg name="paused" value="false"/>
    <arg name="use_sim_time" value="true"/>
    <arg name="gui" value="true"/>
    <arg name="headless" value="false"/>
    <arg name="debug" value="false"/>
  </include>
  
  <param name="robot_description" command="$(find xacro)/xacro $(find robot_description)/urdf/Robot.xacro"/>
  
  
  <node name="weednix_spawn" pkg="gazebo_ros" type="spawn_model" output="screen" respawn="false"
   args="-x -6.0 -y 0 -z 0.15 -Y 0 -R 0 -P 0 -Y 3.1416 -urdf -param robot_description -model weednix">
  </node> 
  
   <!-- <include file="$(find agribot_navigation)/launch/agribot_navigation.launch"/> -->

     <node pkg="visual_servoing" type="row_crop_follower.py" name="row_crop_follower" output="screen" launch-prefix="python3"/> 
  
    <!-- <include file="$(find visual_servoing)/launch/row_crop_follower_with_nh.launch"/> -->
     
     
   <!-- Launch other nodes -->
  <!-- <include file="$(find robot_description)/launch/controller.launch" /> -->


<!-- Launch EKF -->
<node pkg="robot_localization" type="ekf_localization_node" name="ekf_filter_node" output="screen">
    <rosparam command="load" file="$(find weednix_launch)/config/ekf.yaml" />
</node>


  
</launch>
