^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Changelog for package realsense_gazebo_plugin
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

1.1.0 (2020-01-30)
------------------
* Merge branch 'pointcloud_ferrum' into 'ferrum-devel'
  added methods to publish the pointcloud information
  See merge request device/realsense_gazebo_plugin!14
* added methods to publish the pointcloud information
* Update readme to remove explicit mention to REEM-C
* Contributors: Sai Kishor Kothakota, <PERSON>

1.0.4 (2019-12-10)
------------------
* Merge branch 'gazebo_xacro_ferrum' into 'ferrum-devel'
  remove gazebo and URDF xacro
  See merge request device/realsense_gazebo_plugin!12
* remove gazebo and URDF xacro
* Contributors: <PERSON>, <PERSON>

1.0.3 (2019-11-05)
------------------
* Add SYSTEM to include_directories
* Contributors: <PERSON>

1.0.2 (2019-10-30)
------------------
* Merge branch 'mesh-fix-ferrum' into 'ferrum-devel'
  fixed the issue with mesh location
  See merge request device/realsense_gazebo_plugin!10
* fixed the issue with mesh location
* Contributors: Adria Roig, Sai Kishor Kothakota

1.0.1 (2019-10-30)
------------------
* Merge branch 'multi-realsense-ferrum' into 'ferrum-devel'
  Update plugin to support multiple realsense camera's
  See merge request device/realsense_gazebo_plugin!7
* Update plugin to support multiple realsense camera's
* Contributors: Adria Roig, Sai Kishor Kothakota

1.0.0 (2019-09-10)
------------------
* Adapted to latest gazebo API changes
* Contributors: Jordan Palacios

0.0.3 (2019-03-28)
------------------
* Fix licenses for public release
* Contributors: Adria Roig

0.0.2 (2019-03-05)
------------------
* Merge branch 'realsense' into 'master'
  Fix missing installation files
  See merge request device/realsense_gazebo_plugin!2
* Fix missing installation files
* Contributors: Adria Roig

0.0.1 (2019-03-01)
------------------
* Rm unnecessary dependees
* Fix dependencies
* Merge branch 'realsense' into 'master'
  Realsense
  See merge request adriaroig/realsense_gazebo_plugin!1
* Rm unnecessary files
* Rm unneccessary files
* Delete CMakeLists.txt.user
* Initial commit
* Contributors: Adria Roig
