#include <ros.h>
#include <geometry_msgs/Twist.h>
#include <geometry_msgs/Point32.h> // For sending encoder counts AND distances
#include <std_msgs/String.h>       // For debugging messages

// === Motor Pins ===
const int pwmRight = 5;
const int dirRight = 4;
const int pwmLeft  = 8;
const int dirLeft  = 9;

// === Robot Physical Parameters ===
const float WHEEL_SEPARATION = 0.6;      // meters
const float MAX_SPEED = 15;             // m/s -> 255 PWM
const float WHEEL_RADIUS_CM = 19.2;          // Radius in centimeters
// IMPORTANT: Verify this value for your encoders with quadrature decoding.
// This value should represent the total number of counts registered (after 4x multiplication
// by the quadrature logic) for one full wheel revolution.
const float PULSES_PER_REVOLUTION = 46500.0;
const float WHEEL_CIRCUMFERENCE_CM = 2.0 * PI * WHEEL_RADIUS_CM; // Calculate circumference once

const float WHEEL_CIRCUMFERENCE_M = WHEEL_CIRCUMFERENCE_CM / 100.0; // Circumference in meters

// === Encoder definitions (Quadrature) ===
// !! IMPORTANT !! Choose pins that support external interrupts on YOUR Arduino board.
// Example for Arduino Mega (Pins 2, 3, 18, 19 are interrupt capable):
const int rightEncoderPinA = 2;  // Interrupt Pin (e.g., INT.0 on Mega/Uno) 
const int rightEncoderPinB = 3;  // Interrupt Pin (e.g., INT.1 on Mega/Uno)
const int leftEncoderPinA = 18; // Interrupt Pin (e.g., INT.5 on Mega)
const int leftEncoderPinB = 19; // Interrupt Pin (e.g., INT.4 on Mega)
// If using Arduino Uno/Nano, you only have 2 external interrupts (pins 2, 3).
// You might need PinChangeInterrupt library for more pins, or a different hardware setup.

// Encoder counters (updated by quadrature ISRs)
volatile long int rightEncoderCount = 0;
volatile long int leftEncoderCount = 0;

// State variables for quadrature decoding
volatile int lastEncodedRight = 0;
volatile int lastEncodedLeft = 0;

// Timing variables
unsigned long prevTime = 0;
unsigned long lastCmdVelTime = 0;
const unsigned long CMD_VEL_TIMEOUT = 500; // Stop motors if no cmd_vel received in 500ms

// Buffer for debug messages
char debug_buffer[150];

int pwmL_global = 0;
int pwmR_global = 0;


// ROS Node handler
ros::NodeHandle nh;

// Publishers
geometry_msgs::Point32 encoder_count_msg; // Message for raw encoder counts
geometry_msgs::Point32 wheel_distance_msg; // Message for calculated wheel distances (in cm)
std_msgs::String debug_msg;

ros::Publisher enc_count_pub("/encoder_counts", &encoder_count_msg); // Topic for raw counts
ros::Publisher wheel_dist_pub("/wheel_distances", &wheel_distance_msg); // Topic for distances
ros::Publisher debug_pub("/debug_messages", &debug_msg);

// Subscriber Callback for cmd_vel
void cmdVelCallback(const geometry_msgs::Twist &cmd_msg) {
  float linear = cmd_msg.linear.x;
  float angular = cmd_msg.angular.z;

  // Calculate wheel velocities
  float vLeft  = linear - (angular * WHEEL_SEPARATION / 2.0);
  float vRight = linear + (angular * WHEEL_SEPARATION / 2.0);

  // Constrain velocities
  vLeft  = constrain(vLeft, -MAX_SPEED, MAX_SPEED);
  vRight = constrain(vRight, -MAX_SPEED, MAX_SPEED);

  // Convert to PWM values
  int pwmL = int(fabs(vLeft)  / MAX_SPEED * 255);
  int pwmR = int(fabs(vRight) / MAX_SPEED * 255);

  pwmL_global = pwmL;
  pwmR_global = pwmR;

  // Determine motor direction (still needed for motor control)
  bool motor_forward_dir_left = (vLeft >= 0);
  bool motor_forward_dir_right = (vRight >= 0);

  // Apply motor control
  digitalWrite(dirLeft, motor_forward_dir_left ? HIGH : LOW);
  analogWrite(pwmLeft, pwmL);

  digitalWrite(dirRight, motor_forward_dir_right ? HIGH : LOW);
  analogWrite(pwmRight, pwmR);

  // Create debug message using sprintf
  sprintf(debug_buffer, "CmdVel Rx: Lin=%.2f Ang=%.2f | PWM_L: %d DIR_L: %s | PWM_R: %d DIR_R: %s",
          linear, angular,
          pwmL, (motor_forward_dir_left ? "FWD" : "REV"),
          pwmR, (motor_forward_dir_right ? "FWD" : "REV"));

  // Publish the debug message
  debug_msg.data = debug_buffer;
  debug_pub.publish(&debug_msg);

  // Update the timestamp of the last received cmd_vel message
  lastCmdVelTime = millis();
}

ros::Subscriber<geometry_msgs::Twist> sub("cmd_vel", cmdVelCallback);

void setup() {
  // Initialize motor pins
  pinMode(pwmRight, OUTPUT);
  pinMode(dirRight, OUTPUT);
  pinMode(pwmLeft, OUTPUT);
  pinMode(dirLeft, OUTPUT);

  // Initialize encoder pins with pullup resistors
  pinMode(rightEncoderPinA, INPUT_PULLUP);
  pinMode(rightEncoderPinB, INPUT_PULLUP);
  pinMode(leftEncoderPinA, INPUT_PULLUP);
  pinMode(leftEncoderPinB, INPUT_PULLUP);

  // Attach interrupt handlers for BOTH pins of EACH encoder
  // The same ISR is called for any change on either pin of a single encoder
  attachInterrupt(digitalPinToInterrupt(rightEncoderPinA), updateRightEncoder, CHANGE);
  attachInterrupt(digitalPinToInterrupt(rightEncoderPinB), updateRightEncoder, CHANGE);
  attachInterrupt(digitalPinToInterrupt(leftEncoderPinA), updateLeftEncoder, CHANGE);
  attachInterrupt(digitalPinToInterrupt(leftEncoderPinB), updateLeftEncoder, CHANGE);

  // Initialize ROS node and publishers/subscribers
  nh.initNode();
  nh.subscribe(sub);
  nh.advertise(enc_count_pub);
  nh.advertise(wheel_dist_pub);
  nh.advertise(debug_pub);

  // Stop motors initially
  stopMotors();

  // Send initial debug message
  strcpy(debug_buffer, "Robot initialized (Quadrature Enc). Waiting for cmd_vel...");
  debug_msg.data = debug_buffer;
  debug_pub.publish(&debug_msg);

  // Initialize timing variables
  lastCmdVelTime = millis();
  prevTime = millis();
}

void loop() {
  unsigned long currentTime = millis();

  // Check for cmd_vel timeout
  if (currentTime - lastCmdVelTime > CMD_VEL_TIMEOUT) {
    if (pwmL_global != 0 || pwmR_global != 0) {
        stopMotors();
        strcpy(debug_buffer, "CMD_VEL timeout. Motors stopped.");
        debug_msg.data = debug_buffer;
        debug_pub.publish(&debug_msg);
    }
  }

  // --- Local copies of volatile variables ---
  // It's safer to work with copies outside ISRs
  long currentRightEncoderCount;
  long currentLeftEncoderCount;

  noInterrupts(); // Temporarily disable interrupts to safely read volatile variables
  currentRightEncoderCount = rightEncoderCount;
  currentLeftEncoderCount = leftEncoderCount;
  interrupts(); // Re-enable interrupts

  // --- Publish Raw Encoder Counts ---
  encoder_count_msg.x = currentRightEncoderCount;
  encoder_count_msg.y = currentLeftEncoderCount;
  encoder_count_msg.z = 0;
  enc_count_pub.publish(&encoder_count_msg);

 // --- Calculate and Publish Wheel Distances ---
// float distanceRightCm = (currentRightEncoderCount * WHEEL_CIRCUMFERENCE_CM) / PULSES_PER_REVOLUTION; // OLD
// float distanceLeftCm = (currentLeftEncoderCount * WHEEL_CIRCUMFERENCE_CM) / PULSES_PER_REVOLUTION;  // OLD
float distanceRightM = (currentRightEncoderCount * WHEEL_CIRCUMFERENCE_M) / PULSES_PER_REVOLUTION; // NEW (use _M)
float distanceLeftM = (currentLeftEncoderCount * WHEEL_CIRCUMFERENCE_M) / PULSES_PER_REVOLUTION;  // NEW (use _M)

// wheel_distance_msg.x = distanceRightCm; // OLD
// wheel_distance_msg.y = distanceLeftCm;  // OLD
wheel_distance_msg.x = distanceRightM; // NEW (use meter variable)
wheel_distance_msg.y = distanceLeftM;  // NEW (use meter variable)
wheel_distance_msg.z = 0; // Keep z as 0
wheel_dist_pub.publish(&wheel_distance_msg);

  // Process ROS callbacks
  nh.spinOnce();
  delay(10);
// Occasionally publish status debug message
if (currentTime - prevTime > 1000) { // Every second

    // --- START OF MODIFICATIONS ---

    // 1. Declare character arrays (buffers) for the string versions of the floats
    char distR_str[10]; // Buffer for right distance string (adjust size if needed)
    char distL_str[10]; // Buffer for left distance string (adjust size if needed)

    // 2. Convert floats to strings using dtostrf()
    // dtostrf(float_value, min_width, num_digits_after_decimal, output_buffer);
     dtostrf(distanceRightM, 4, 3, distR_str); // (use meter variable, maybe 3 decimal places for meters?)
     dtostrf(distanceLeftM,  4, 3, distL_str); // (use meter variable, maybe 3 decimal places for meters?)

    // 3. Modify sprintf to use %s and the string buffers
    sprintf(debug_buffer, "Status | Enc Cnts R:%ld L:%ld | Dist (m) R:%s L:%s | PWM R:%d L:%d",
            currentRightEncoderCount, currentLeftEncoderCount, // Keep %ld for longs
            distR_str, distL_str, // Use %s and pass the string buffers
            pwmR_global, pwmL_global); // Keep %d for ints

    // --- END OF MODIFICATIONS ---

    // This part remains the same
    debug_msg.data = debug_buffer;
    debug_pub.publish(&debug_msg);
    prevTime = currentTime;
}

// The closing brace for loop() might be here or later depending on your full code
// }
}

void stopMotors() {
  pwmL_global = 0;
  pwmR_global = 0;
  analogWrite(pwmLeft, 0);
  digitalWrite(dirLeft, LOW);
  analogWrite(pwmRight, 0);
  digitalWrite(dirRight, LOW);
}

// --- Quadrature Encoder Interrupt Service Routines ---

// ISR for Right Encoder (triggered by changes on PinA or PinB)
void updateRightEncoder() {
  // Read the current state of the encoder pins
  int MSB = digitalRead(rightEncoderPinA); // Most Significant Bit (Pin A)
  int LSB = digitalRead(rightEncoderPinB); // Least Significant Bit (Pin B)
  int encoded = (MSB << 1) | LSB;         // Combine the two pin values (gives 00, 01, 10, or 11)

  // Create a state value that combines the previous and current readings
  // (lastEncoded holds the previous state, encoded holds the current state)
  int sum = (lastEncodedRight << 2) | encoded;

  // Update the encoder count based on valid state transitions
  // These specific state transitions correspond to clockwise rotation
  if (sum == 0b1101 || sum == 0b0100 || sum == 0b0010 || sum == 0b1011) {
    rightEncoderCount++;
  }
  // These specific state transitions correspond to counter-clockwise rotation
  if (sum == 0b1110 || sum == 0b0111 || sum == 0b0001 || sum == 0b1000) {
    rightEncoderCount--;
  }

  lastEncodedRight = encoded; // Store current state for the next interrupt
}

// ISR for Left Encoder (triggered by changes on PinA or PinB)
void updateLeftEncoder() {
  // Read the current state of the encoder pins
  int MSB = digitalRead(leftEncoderPinA); // Most Significant Bit (Pin A)
  int LSB = digitalRead(leftEncoderPinB); // Least Significant Bit (Pin B)
  int encoded = (MSB << 1) | LSB;         // Combine the two pin values

  // Create a state value that combines the previous and current readings
  int sum = (lastEncodedLeft << 2) | encoded;

  // Update the encoder count based on valid state transitions
  // Clockwise transitions
  if (sum == 0b1101 || sum == 0b0100 || sum == 0b0010 || sum == 0b1011) {
    leftEncoderCount++;
  }
  // Counter-clockwise transitions
  if (sum == 0b1110 || sum == 0b0111 || sum == 0b0001 || sum == 0b1000) {
    leftEncoderCount--;
  }

  lastEncodedLeft = encoded; // Store current state for the next interrupt
}