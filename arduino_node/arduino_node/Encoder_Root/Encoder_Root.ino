#include <Arduino.h>

// Encoder pin definitions
const int encoder_Pin_1 = 18 ;//18; // Interrupt pin 0
const int encoder_Pin_2 = 19 ;//19;// Interrupt pin 1

// Global variables updated in the ISR
volatile long encoderValue = 0;
volatile int lastEncoded = 0;

// Constants for distance calculation
const float WHEEL_RADIUS_CM = 19.2;      // Radius (ray length) in centimeters
const float PULSES_PER_REVOLUTION = 46500.0; // 360 pulses * 4 (for quadrature decoding)//1440.0

void setup() {
  Serial.begin(9600);
  
  // Set encoder pins as inputs with pullup resistors enabled
  pinMode(encoder_Pin_1, INPUT_PULLUP);
  pinMode(encoder_Pin_2, INPUT_PULLUP);
  
  // Attach interrupts to both encoder channels for any change
  attachInterrupt(digitalPinToInterrupt(encoder_Pin_1), updateEncoder, CHANGE);
  attachInterrupt(digitalPinToInterrupt(encoder_Pin_2), updateEncoder, CHANGE);
}

void loop() {
  // Calculate wheel circumference (in cm)
  float circumference = 2 * PI * WHEEL_RADIUS_CM;
  
  // Compute the traveled distance based on encoder counts
  float distance = (encoderValue * circumference) / PULSES_PER_REVOLUTION;
  
  // Print encoder count and calculated distance
  Serial.print("Encoder Count: ");
  Serial.print(encoderValue);
  Serial.print(" -> Distance: ");
  Serial.print(distance, 2); // 2 decimal places
  Serial.println(" cm");
  
}

void updateEncoder() {
  // Read the current state of the encoder pins
  int MSB = digitalRead(encoder_Pin_1); // Most Significant Bit
  int LSB = digitalRead(encoder_Pin_2); // Least Significant Bit
  int encoded = (MSB << 1) | LSB;         // Combine the two pin values
  
  // Create a state value that combines the previous and current readings
  int sum = (lastEncoded << 2) | encoded;
  
  // Update the encoder count based on valid state transitions
  if (sum == 0b1101 || sum == 0b0100 || sum == 0b0010 || sum == 0b1011) {
    encoderValue++;
  }
  if (sum == 0b1110 || sum == 0b0111 || sum == 0b0001 || sum == 0b1000) {
    encoderValue--;
  }
  
  lastEncoded = encoded; // Store current state for next ISR call
}
